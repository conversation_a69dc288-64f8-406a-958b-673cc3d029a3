import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface LanguageContextType {
  language: string;
  setLanguage: (lang: string) => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

interface Translations {
  [key: string]: {
    [key: string]: string;
  };
}

const translations: Translations = {
  en: {
    // Navigation
    'nav.hostManagement': 'Host Management',
    'nav.taskManagement': 'Task Management',
    'nav.settings': 'Settings',
    
    // Host Management
    'host.title': 'SSH Hosts',
    'host.addHost': 'Add Host',
    'host.editHost': 'Edit Host',
    'host.name': 'Name',
    'host.hostname': 'Hostname/IP',
    'host.port': 'Port',
    'host.username': 'Username',
    'host.password': 'Password',
    'host.privateKey': 'Private Key',
    'host.description': 'Description',
    'host.authMethod': 'Auth Method',
    'host.created': 'Created',
    'host.actions': 'Actions',
    'host.testConnection': 'Test Connection',
    'host.edit': 'Edit',
    'host.delete': 'Delete',
    'host.deleteConfirm': 'Are you sure you want to delete this host?',
    'host.exportCSV': 'Export CSV',
    'host.importCSV': 'Import CSV',
    'host.exportSuccess': 'Hosts exported successfully',
    'host.importSuccess': 'Hosts imported successfully',
    'host.importError': 'Failed to import hosts',
    'host.importing': 'Importing hosts...',
    'host.noValidData': 'No valid host data found in CSV file',
    'host.parseError': 'Failed to parse CSV file',
    'host.fileTooLarge': 'File is too large (max 5MB)',
    'host.tooManyRows': 'Too many rows (max 1000)',
    'host.fileReadError': 'Failed to read file',

    // Host Form
    'host.nameRequired': 'Please enter host name',
    'host.namePlaceholder': 'Enter host name',
    'host.hostnameRequired': 'Please enter hostname or IP',
    'host.hostnamePlaceholder': 'Enter hostname or IP address',
    'host.portRequired': 'Please enter port',
    'host.usernameRequired': 'Please enter username',
    'host.usernamePlaceholder': 'Enter username',
    'host.passwordPlaceholder': 'Enter password (optional if using private key)',
    'host.privateKeyPlaceholder': 'Enter private key (optional if using password)',
    'host.descriptionPlaceholder': 'Enter description (optional)',

    // Batch Operations
    'host.batchDelete': 'Batch Delete',
    'host.batchDeleteConfirm': 'Are you sure you want to delete selected hosts?',
    'host.batchDeleteSuccess': 'Hosts deleted successfully',
    'host.batchDeleteError': 'Failed to delete hosts',
    'host.deleting': 'Deleting hosts...',
    'host.selectHostsToDelete': 'Please select hosts to delete',
    'host.selectedCount': 'Selected',
    'host.tooManySelected': 'Too many hosts selected (max 50)',
    'host.noHostsToDelete': 'No hosts to delete',
    
    // Task Management
    'task.title': 'Tasks',
    'task.createTask': 'Create Task',
    'task.name': 'Task Name',
    'task.type': 'Task Type',
    'task.details': 'Details',
    'task.command': 'Command',
    'task.script': 'Script',
    'task.file_transfer': 'File Transfer',
    'task.hosts': 'Hosts',
    'task.status': 'Status',
    'task.parallel': 'Parallel',
    'task.created': 'Created',
    'task.actions': 'Actions',
    'task.execute': 'Execute Task',
    'task.cancel': 'Cancel Task',
    'task.viewLogs': 'View Logs',
    'task.logs': 'Task Logs',
    'task.delete': 'Delete',
    'task.deleteConfirm': 'Are you sure you want to delete this task?',

    // Task Status
    'task.status.pending': 'Pending',
    'task.status.running': 'Running',
    'task.status.completed': 'Completed',
    'task.status.failed': 'Failed',
    'task.status.cancelled': 'Cancelled',

    // Task Operations
    'task.deleteSuccess': 'Task deleted successfully',
    'task.deleteError': 'Failed to delete task',
    'task.batchDelete': 'Batch Delete',
    'task.batchDeleteConfirm': 'Are you sure you want to delete selected tasks?',
    'task.batchDeleteSuccess': 'Tasks deleted successfully',
    'task.batchDeleteError': 'Failed to delete tasks',
    'task.deleting': 'Deleting tasks...',
    'task.selectTasksToDelete': 'Please select tasks to delete',
    'task.selectedCount': 'Selected',
    'task.tooManySelected': 'Too many tasks selected (max 50)',
    'task.noTasksToDelete': 'No tasks to delete',
    
    // Common
    'common.yes': 'Yes',
    'common.no': 'No',
    'common.ok': 'OK',
    'common.cancel': 'Cancel',
    'common.save': 'Save',
    'common.edit': 'Edit',
    'common.delete': 'Delete',
    'common.loading': 'Loading...',
    'common.success': 'Success',
    'common.error': 'Error',
    'common.warning': 'Warning',
    'common.info': 'Info'
  },
  zh: {
    // Navigation
    'nav.hostManagement': '主机管理',
    'nav.taskManagement': '任务管理',
    'nav.settings': '设置',
    
    // Host Management
    'host.title': 'SSH主机',
    'host.addHost': '添加主机',
    'host.editHost': '编辑主机',
    'host.name': '名称',
    'host.hostname': '主机名/IP',
    'host.port': '端口',
    'host.username': '用户名',
    'host.password': '密码',
    'host.privateKey': '私钥',
    'host.description': '描述',
    'host.authMethod': '认证方式',
    'host.created': '创建时间',
    'host.actions': '操作',
    'host.testConnection': '测试连接',
    'host.edit': '编辑',
    'host.delete': '删除',
    'host.deleteConfirm': '确定要删除这个主机吗？',
    'host.exportCSV': '导出CSV',
    'host.importCSV': '导入CSV',
    'host.exportSuccess': '主机导出成功',
    'host.importSuccess': '主机导入成功',
    'host.importError': '主机导入失败',
    'host.importing': '正在导入主机...',
    'host.noValidData': 'CSV文件中没有找到有效的主机数据',
    'host.parseError': 'CSV文件解析失败',
    'host.fileTooLarge': '文件过大（最大5MB）',
    'host.tooManyRows': '行数过多（最多1000行）',
    'host.fileReadError': '文件读取失败',

    // Host Form
    'host.nameRequired': '请输入主机名称',
    'host.namePlaceholder': '输入主机名称',
    'host.hostnameRequired': '请输入主机名或IP地址',
    'host.hostnamePlaceholder': '输入主机名或IP地址',
    'host.portRequired': '请输入端口号',
    'host.usernameRequired': '请输入用户名',
    'host.usernamePlaceholder': '输入用户名',
    'host.passwordPlaceholder': '输入密码（使用私钥时可选）',
    'host.privateKeyPlaceholder': '输入私钥（使用密码时可选）',
    'host.descriptionPlaceholder': '输入描述（可选）',

    // Batch Operations
    'host.batchDelete': '批量删除',
    'host.batchDeleteConfirm': '确定要删除选中的主机吗？',
    'host.batchDeleteSuccess': '主机删除成功',
    'host.batchDeleteError': '主机删除失败',
    'host.deleting': '正在删除主机...',
    'host.selectHostsToDelete': '请选择要删除的主机',
    'host.selectedCount': '已选择',
    'host.tooManySelected': '选择的主机过多（最多50个）',
    'host.noHostsToDelete': '没有主机需要删除',
    
    // Task Management
    'task.title': '任务',
    'task.createTask': '创建任务',
    'task.name': '任务名称',
    'task.type': '任务类型',
    'task.details': '详情',
    'task.command': '命令',
    'task.script': '脚本',
    'task.file_transfer': '文件传输',
    'task.hosts': '主机',
    'task.status': '状态',
    'task.parallel': '并行',
    'task.created': '创建时间',
    'task.actions': '操作',
    'task.execute': '执行任务',
    'task.cancel': '取消任务',
    'task.viewLogs': '查看日志',
    'task.logs': '任务日志',
    'task.delete': '删除',
    'task.deleteConfirm': '确定要删除这个任务吗？',

    // Task Status
    'task.status.pending': '等待中',
    'task.status.running': '运行中',
    'task.status.completed': '已完成',
    'task.status.failed': '失败',
    'task.status.cancelled': '已取消',

    // Task Operations
    'task.deleteSuccess': '任务删除成功',
    'task.deleteError': '任务删除失败',
    'task.batchDelete': '批量删除',
    'task.batchDeleteConfirm': '确定要删除选中的任务吗？',
    'task.batchDeleteSuccess': '任务删除成功',
    'task.batchDeleteError': '任务删除失败',
    'task.deleting': '正在删除任务...',
    'task.selectTasksToDelete': '请选择要删除的任务',
    'task.selectedCount': '已选择',
    'task.tooManySelected': '选择的任务过多（最多50个）',
    'task.noTasksToDelete': '没有任务需要删除',
    
    // Common
    'common.yes': '是',
    'common.no': '否',
    'common.ok': '确定',
    'common.cancel': '取消',
    'common.save': '保存',
    'common.edit': '编辑',
    'common.delete': '删除',
    'common.loading': '加载中...',
    'common.success': '成功',
    'common.error': '错误',
    'common.warning': '警告',
    'common.info': '信息'
  }
};

interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [language, setLanguageState] = useState<string>('en');

  useEffect(() => {
    const savedLanguage = localStorage.getItem('app-language') || 'en';
    setLanguageState(savedLanguage);

    const handleLanguageChange = (event: CustomEvent) => {
      setLanguageState(event.detail);
    };

    window.addEventListener('languageChange', handleLanguageChange as EventListener);
    return () => {
      window.removeEventListener('languageChange', handleLanguageChange as EventListener);
    };
  }, []);

  const setLanguage = (lang: string) => {
    setLanguageState(lang);
    localStorage.setItem('app-language', lang);
    window.dispatchEvent(new CustomEvent('languageChange', { detail: lang }));
  };

  const t = (key: string): string => {
    return translations[language]?.[key] || translations.en[key] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
