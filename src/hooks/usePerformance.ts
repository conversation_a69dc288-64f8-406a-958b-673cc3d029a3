import { useState, useEffect, useRef, useCallback, useMemo } from 'react';

/**
 * 防抖Hook
 * @param value 需要防抖的值
 * @param delay 延迟时间（毫秒）
 * @returns 防抖后的值
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * 节流Hook
 * @param callback 需要节流的函数
 * @param delay 延迟时间（毫秒）
 * @returns 节流后的函数
 */
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastRun = useRef(Date.now());

  return useCallback(
    ((...args) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args);
        lastRun.current = Date.now();
      }
    }) as T,
    [callback, delay]
  );
}

/**
 * 虚拟滚动Hook
 * @param items 所有项目
 * @param containerHeight 容器高度
 * @param itemHeight 单项高度
 * @returns 虚拟滚动相关状态和方法
 */
export function useVirtualScroll<T>(
  items: T[],
  containerHeight: number,
  itemHeight: number
) {
  const [scrollTop, setScrollTop] = useState(0);

  const visibleCount = Math.ceil(containerHeight / itemHeight);
  const totalHeight = items.length * itemHeight;
  const startIndex = Math.floor(scrollTop / itemHeight);
  const endIndex = Math.min(startIndex + visibleCount + 1, items.length);

  const visibleItems = useMemo(
    () => items.slice(startIndex, endIndex),
    [items, startIndex, endIndex]
  );

  const offsetY = startIndex * itemHeight;

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);

  return {
    visibleItems,
    totalHeight,
    offsetY,
    handleScroll,
    startIndex,
    endIndex,
  };
}

/**
 * 异步状态管理Hook
 * @param asyncFunction 异步函数
 * @returns 异步状态和执行函数
 */
export function useAsync<T, P extends any[]>(
  asyncFunction: (...args: P) => Promise<T>
) {
  const [state, setState] = useState<{
    data: T | null;
    loading: boolean;
    error: Error | null;
  }>({
    data: null,
    loading: false,
    error: null,
  });

  const execute = useCallback(
    async (...args: P) => {
      setState({ data: null, loading: true, error: null });
      try {
        const result = await asyncFunction(...args);
        setState({ data: result, loading: false, error: null });
        return result;
      } catch (error) {
        setState({ data: null, loading: false, error: error as Error });
        throw error;
      }
    },
    [asyncFunction]
  );

  return { ...state, execute };
}

/**
 * 内存泄漏防护Hook
 * @param callback 回调函数
 * @param deps 依赖数组
 * @returns 安全的回调函数
 */
export function useSafeCallback<T extends (...args: any[]) => any>(
  callback: T,
  deps: React.DependencyList
): T {
  const isMountedRef = useRef(true);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  return useCallback(
    ((...args) => {
      if (isMountedRef.current) {
        return callback(...args);
      }
    }) as T,
    [callback, ...deps]
  );
}

/**
 * 批量状态更新Hook
 * @param initialState 初始状态
 * @returns 状态和批量更新函数
 */
export function useBatchedState<T extends Record<string, any>>(initialState: T) {
  const [state, setState] = useState<T>(initialState);
  const pendingUpdates = useRef<Partial<T>>({});
  const updateTimeout = useRef<NodeJS.Timeout>();

  const batchUpdate = useCallback((updates: Partial<T>) => {
    pendingUpdates.current = { ...pendingUpdates.current, ...updates };

    if (updateTimeout.current) {
      clearTimeout(updateTimeout.current);
    }

    updateTimeout.current = setTimeout(() => {
      setState(prevState => ({ ...prevState, ...pendingUpdates.current }));
      pendingUpdates.current = {};
    }, 16); // 批量更新，大约60fps
  }, []);

  const immediateUpdate = useCallback((updates: Partial<T>) => {
    if (updateTimeout.current) {
      clearTimeout(updateTimeout.current);
    }
    setState(prevState => ({ ...prevState, ...pendingUpdates.current, ...updates }));
    pendingUpdates.current = {};
  }, []);

  return { state, batchUpdate, immediateUpdate };
}

/**
 * 性能监控Hook
 * @param name 监控名称
 * @returns 性能测量函数
 */
export function usePerformanceMonitor(name: string) {
  const startTime = useRef<number>();

  const start = useCallback(() => {
    startTime.current = performance.now();
  }, []);

  const end = useCallback(() => {
    if (startTime.current) {
      const duration = performance.now() - startTime.current;
      console.log(`[Performance] ${name}: ${duration.toFixed(2)}ms`);
      return duration;
    }
    return 0;
  }, [name]);

  const measure = useCallback(async <T>(fn: () => Promise<T>): Promise<T> => {
    start();
    try {
      const result = await fn();
      end();
      return result;
    } catch (error) {
      end();
      throw error;
    }
  }, [start, end]);

  return { start, end, measure };
}

/**
 * 缓存Hook
 * @param key 缓存键
 * @param fetcher 数据获取函数
 * @param ttl 缓存时间（毫秒）
 * @returns 缓存的数据和刷新函数
 */
export function useCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  ttl: number = 5 * 60 * 1000 // 默认5分钟
) {
  const [state, setState] = useState<{
    data: T | null;
    loading: boolean;
    error: Error | null;
    timestamp: number;
  }>({
    data: null,
    loading: false,
    error: null,
    timestamp: 0,
  });

  const cache = useRef<Map<string, any>>(new Map());

  const isExpired = useCallback((timestamp: number) => {
    return Date.now() - timestamp > ttl;
  }, [ttl]);

  const fetch = useCallback(async (forceRefresh = false) => {
    const cached = cache.current.get(key);

    if (cached && !forceRefresh && !isExpired(cached.timestamp)) {
      setState({
        data: cached.data,
        loading: false,
        error: null,
        timestamp: cached.timestamp,
      });
      return cached.data;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const data = await fetcher();
      const timestamp = Date.now();

      cache.current.set(key, { data, timestamp });
      setState({ data, loading: false, error: null, timestamp });

      return data;
    } catch (error) {
      setState(prev => ({ ...prev, loading: false, error: error as Error }));
      throw error;
    }
  }, [key, fetcher, isExpired]);

  const invalidate = useCallback(() => {
    cache.current.delete(key);
  }, [key]);

  useEffect(() => {
    fetch();
  }, [fetch]);

  return {
    ...state,
    refetch: () => fetch(true),
    invalidate,
  };
}

/**
 * 窗口大小Hook
 * @returns 当前窗口尺寸
 */
export function useWindowSize() {
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  });

  useEffect(() => {
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    }

    const debouncedResize = debounce(handleResize, 100);
    window.addEventListener('resize', debouncedResize);

    return () => window.removeEventListener('resize', debouncedResize);
  }, []);

  return windowSize;
}

/**
 * 简单的防抖函数
 */
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * 组件可见性Hook
 * @param threshold 可见阈值
 * @returns ref和可见状态
 */
export function useIntersectionObserver(threshold = 0.1) {
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      { threshold }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current);
      }
    };
  }, [threshold]);

  return { ref, isVisible };
}
