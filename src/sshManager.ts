import { Client } from 'ssh2';

export class SSHManager {
  private connections = new Map<string, Client>();

  connect(config: { id: string; host: string; port: number; username: string; password: string }) {
    const client = new Client();
    client.connect({
      host: config.host,
      port: config.port,
      username: config.username,
      password: config.password
    });

    this.connections.set(config.id, client);
    return client;
  }

  getConnection(id: string) {
    return this.connections.get(id);
  }
}