import React, { useState, useEffect } from 'react';
import { Card, Select, Typography, Space, Divider, message } from 'antd';
import { GlobalOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;

interface LanguageConfig {
  code: string;
  name: string;
  nativeName: string;
}

const languages: LanguageConfig[] = [
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'zh', name: 'Chinese', nativeName: '中文' }
];

const Settings: React.FC = () => {
  const [currentLanguage, setCurrentLanguage] = useState<string>('en');

  useEffect(() => {
    // Load saved language from localStorage
    const savedLanguage = localStorage.getItem('app-language') || 'en';
    setCurrentLanguage(savedLanguage);
  }, []);

  const handleLanguageChange = (language: string) => {
    setCurrentLanguage(language);
    localStorage.setItem('app-language', language);
    message.success(
      language === 'zh' 
        ? '语言设置已保存' 
        : 'Language setting saved'
    );
    
    // Trigger a custom event to notify other components
    window.dispatchEvent(new CustomEvent('languageChange', { detail: language }));
  };

  const getText = (key: string) => {
    const texts: Record<string, Record<string, string>> = {
      en: {
        settings: 'Settings',
        language: 'Language',
        languageDesc: 'Choose your preferred language for the application interface',
        general: 'General Settings',
        appearance: 'Appearance & Language'
      },
      zh: {
        settings: '设置',
        language: '语言',
        languageDesc: '选择应用程序界面的首选语言',
        general: '常规设置',
        appearance: '外观与语言'
      }
    };
    return texts[currentLanguage]?.[key] || texts.en[key];
  };

  return (
    <div style={{ padding: '0' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Card>
          <Title level={4}>
            <GlobalOutlined style={{ marginRight: 8 }} />
            {getText('appearance')}
          </Title>
          <Divider />
          
          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            <div>
              <Text strong style={{ display: 'block', marginBottom: 8 }}>
                {getText('language')}
              </Text>
              <Text type="secondary" style={{ display: 'block', marginBottom: 16 }}>
                {getText('languageDesc')}
              </Text>
              <Select
                value={currentLanguage}
                onChange={handleLanguageChange}
                style={{ width: 200 }}
                size="large"
              >
                {languages.map(lang => (
                  <Option key={lang.code} value={lang.code}>
                    <Space>
                      <span>{lang.nativeName}</span>
                      <Text type="secondary">({lang.name})</Text>
                    </Space>
                  </Option>
                ))}
              </Select>
            </div>
          </Space>
        </Card>

        <Card>
          <Title level={4}>
            {getText('general')}
          </Title>
          <Divider />
          <Text type="secondary">
            {currentLanguage === 'zh' 
              ? '更多设置选项即将推出...' 
              : 'More settings options coming soon...'}
          </Text>
        </Card>
      </Space>
    </div>
  );
};

export default Settings;
