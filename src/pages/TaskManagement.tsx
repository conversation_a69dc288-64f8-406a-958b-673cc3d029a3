import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Select, 
  message, 
  Popconfirm,
  Tag,
  Space,
  Tooltip,
  Switch,
  Card,
  Typography,
  Drawer
} from 'antd';
import { 
  PlusOutlined, 
  PlayCircleOutlined, 
  StopOutlined, 
  DeleteOutlined, 
  FileTextOutlined,
  EyeOutlined 
} from '@ant-design/icons';
import { Task, CreateTaskRequest, Host, LogEntry } from '../types';
import { taskApi, hostApi, logApi } from '../services/api';
import { listen } from '@tauri-apps/api/event';
import TaskLogViewer from '../components/TaskLogViewer';
import { useLanguage } from '../contexts/LanguageContext';
import { createCustomPagination } from '../components/CustomPagination';

const { TextArea } = Input;
const { Option } = Select;
const { Text } = Typography;

const TaskManagement: React.FC = () => {
  const { t } = useLanguage();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [hosts, setHosts] = useState<Host[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [logDrawerVisible, setLogDrawerVisible] = useState(false);
  const [selectedTaskId, setSelectedTaskId] = useState<number | null>(null);
  const [runningTasks, setRunningTasks] = useState<Set<number>>(new Set());
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [form] = Form.useForm();

  useEffect(() => {
    loadTasks();
    loadHosts();
    
    // Listen for real-time task log updates
    const unlisten = listen('task_log', (event) => {
      console.log('Received task log:', event.payload);
    });

    return () => {
      unlisten.then(fn => fn());
    };
  }, []);

  const loadTasks = async () => {
    setLoading(true);
    try {
      const taskList = await taskApi.getTasks();
      setTasks(taskList);
      
      // Check which tasks are currently running
      const running = new Set<number>();
      for (const task of taskList) {
        if (task.status === 'running') {
          const isRunning = await taskApi.isTaskRunning(task.id);
          if (isRunning) {
            running.add(task.id);
          }
        }
      }
      setRunningTasks(running);
    } catch (error) {
      message.error('Failed to load tasks');
      console.error('Error loading tasks:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadHosts = async () => {
    try {
      const hostList = await hostApi.getHosts();
      setHosts(hostList);
    } catch (error) {
      message.error('Failed to load hosts');
      console.error('Error loading hosts:', error);
    }
  };

  const handleCreateTask = () => {
    form.resetFields();
    form.setFieldsValue({ task_type: 'command', parallel: false });
    setModalVisible(true);
  };

  const handleSubmit = async (values: any) => {
    try {
      const createRequest: CreateTaskRequest = {
        ...values,
        host_ids: values.host_ids || [],
      };
      await taskApi.createTask(createRequest);
      message.success('Task created successfully');
      setModalVisible(false);
      loadTasks();
    } catch (error) {
      message.error('Failed to create task');
      console.error('Error creating task:', error);
    }
  };

  const handleExecuteTask = async (taskId: number) => {
    try {
      await taskApi.executeTask(taskId);
      message.success('Task execution started');
      setRunningTasks(prev => new Set(prev).add(taskId));
      
      // Subscribe to task logs for real-time updates
      await logApi.subscribeTaskLogs(taskId);
      
      // Refresh task list to update status
      setTimeout(() => loadTasks(), 1000);
    } catch (error) {
      message.error('Failed to execute task');
      console.error('Error executing task:', error);
    }
  };

  const handleCancelTask = async (taskId: number) => {
    try {
      await taskApi.cancelTask(taskId);
      message.success('Task cancelled');
      setRunningTasks(prev => {
        const newSet = new Set(prev);
        newSet.delete(taskId);
        return newSet;
      });
      loadTasks();
    } catch (error) {
      message.error('Failed to cancel task');
      console.error('Error cancelling task:', error);
    }
  };

  const handleDeleteTask = async (taskId: number) => {
    try {
      await taskApi.deleteTask(taskId);
      message.success(t('task.deleteSuccess'));
      loadTasks();
    } catch (error) {
      message.error(t('task.deleteError'));
      console.error('Error deleting task:', error);
    }
  };

  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning(t('task.selectTasksToDelete'));
      return;
    }

    // 限制批量删除数量
    if (selectedRowKeys.length > 50) {
      message.error(t('task.tooManySelected'));
      return;
    }

    // 显示进度提示
    const hideLoading = message.loading(t('task.deleting'), 0);

    try {
      const taskIds = selectedRowKeys.map(id => Number(id));

      // 使用真正的批量删除API
      const deletedIds = await taskApi.deleteTasksBatch(taskIds);

      const successCount = deletedIds.length;
      const errorCount = taskIds.length - successCount;

      // 显示结果
      if (successCount > 0) {
        message.success(t('task.batchDeleteSuccess') + ` (${successCount})`);
        setSelectedRowKeys([]);
        loadTasks(); // 重新加载任务列表
      }

      if (errorCount > 0) {
        message.warning(t('task.batchDeleteError') + ` (${errorCount})`);
      }

      if (successCount === 0) {
        message.info(t('task.noTasksToDelete'));
      }

    } catch (error) {
      console.error('Batch delete tasks error:', error);
      message.error(t('task.batchDeleteError'));
    } finally {
      hideLoading();
    }
  };

  const handleViewLogs = (taskId: number) => {
    setSelectedTaskId(taskId);
    setLogDrawerVisible(true);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
    onSelectAll: (selected: boolean, selectedRows: Task[], changeRows: Task[]) => {
      if (selected) {
        setSelectedRowKeys(tasks.map(task => task.id));
      } else {
        setSelectedRowKeys([]);
      }
    },
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'default';
      case 'running':
        return 'processing';
      case 'completed':
        return 'success';
      case 'failed':
        return 'error';
      case 'cancelled':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getTaskTypeColor = (taskType: string) => {
    switch (taskType) {
      case 'command':
        return 'blue';
      case 'script':
        return 'green';
      case 'file_transfer':
        return 'orange';
      default:
        return 'default';
    }
  };

  const columns = [
    {
      title: t('task.name'),
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: t('task.type'),
      dataIndex: 'task_type',
      key: 'task_type',
      width: 120,
      render: (taskType: string) => (
        <Tag color={getTaskTypeColor(taskType)}>
          {t(`task.${taskType}`)}
        </Tag>
      ),
    },
    {
      title: t('task.details'),
      key: 'details',
      width: 200,
      render: (record: Task) => {
        if (record.command) {
          return (
            <Tooltip title={record.command}>
              <Text ellipsis style={{ maxWidth: 180 }}>
                {record.command}
              </Text>
            </Tooltip>
          );
        }
        if (record.script_content) {
          return (
            <Tooltip title={record.script_content}>
              <Text ellipsis style={{ maxWidth: 180 }}>
                {record.script_content}
              </Text>
            </Tooltip>
          );
        }
        if (record.source_path && record.target_path) {
          return (
            <Tooltip title={`${record.source_path} → ${record.target_path}`}>
              <Text ellipsis style={{ maxWidth: 180 }}>
                {record.source_path} → {record.target_path}
              </Text>
            </Tooltip>
          );
        }
        return '-';
      },
    },
    {
      title: t('task.hosts'),
      key: 'hosts',
      width: 150,
      render: (record: Task) => {
        try {
          const hostIds: number[] = JSON.parse(record.host_ids);
          const hostNames = hostIds.map(id => {
            const host = hosts.find(h => h.id === id);
            return host ? host.name : `Host ${id}`;
          });
          return hostNames.join(', ');
        } catch {
          return 'Invalid';
        }
      },
    },
    {
      title: t('task.status'),
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {t(`task.status.${status}`)}
        </Tag>
      ),
    },
    {
      title: t('task.parallel'),
      dataIndex: 'parallel',
      key: 'parallel',
      width: 80,
      render: (parallel: boolean) => (
        <Tag color={parallel ? 'green' : 'default'}>
          {parallel ? t('common.yes') : t('common.no')}
        </Tag>
      ),
    },
    {
      title: t('task.created'),
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: t('task.actions'),
      key: 'actions',
      width: 150,
      render: (record: Task) => (
        <Space>
          {record.status === 'pending' && (
            <Tooltip title={t('task.execute')}>
              <Button
                icon={<PlayCircleOutlined />}
                size="small"
                type="primary"
                onClick={() => handleExecuteTask(record.id)}
              />
            </Tooltip>
          )}

          {record.status === 'running' && (
            <Tooltip title={t('task.cancel')}>
              <Button
                icon={<StopOutlined />}
                size="small"
                danger
                onClick={() => handleCancelTask(record.id)}
              />
            </Tooltip>
          )}

          <Tooltip title={t('task.viewLogs')}>
            <Button
              icon={<EyeOutlined />}
              size="small"
              onClick={() => handleViewLogs(record.id)}
            />
          </Tooltip>

          {record.status !== 'running' && (
            <Popconfirm
              title={t('task.deleteConfirm')}
              onConfirm={() => handleDeleteTask(record.id)}
              okText={t('common.yes')}
              cancelText={t('common.no')}
            >
              <Tooltip title={t('task.delete')}>
                <Button icon={<DeleteOutlined />} size="small" danger />
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 16, display: 'flex', gap: 8 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleCreateTask}
        >
          {t('task.createTask')}
        </Button>

        <Popconfirm
          title={t('task.batchDeleteConfirm')}
          description={`${t('task.selectedCount')}: ${selectedRowKeys.length}`}
          onConfirm={handleBatchDelete}
          okText={t('common.yes')}
          cancelText={t('common.no')}
          disabled={selectedRowKeys.length === 0}
        >
          <Button
            icon={<DeleteOutlined />}
            danger
            disabled={selectedRowKeys.length === 0}
          >
            {t('task.batchDelete')} ({selectedRowKeys.length})
          </Button>
        </Popconfirm>
      </div>

      <Table
        columns={columns}
        dataSource={tasks}
        rowKey="id"
        loading={loading}
        pagination={createCustomPagination({
          total: tasks.length,
          current: currentPage,
          pageSize: pageSize,
          onChange: (page, size) => {
            setCurrentPage(page);
            if (size) {
              setPageSize(size);
            }
          },
          onShowSizeChange: (current, size) => {
            setCurrentPage(1); // 重置到第一页
            setPageSize(size);
          },
        })}
        rowSelection={rowSelection}
      />

      <Modal
        title="Create Task"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="Task Name"
            rules={[{ required: true, message: 'Please enter task name' }]}
          >
            <Input placeholder="Enter task name" />
          </Form.Item>

          <Form.Item
            name="task_type"
            label="Task Type"
            rules={[{ required: true, message: 'Please select task type' }]}
          >
            <Select placeholder="Select task type">
              <Option value="command">Command Execution</Option>
              <Option value="script">Script Execution</Option>
              <Option value="file_transfer">File Transfer</Option>
            </Select>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.task_type !== currentValues.task_type
            }
          >
            {({ getFieldValue }) => {
              const taskType = getFieldValue('task_type');
              
              if (taskType === 'command') {
                return (
                  <Form.Item
                    name="command"
                    label="Command"
                    rules={[{ required: true, message: 'Please enter command' }]}
                  >
                    <TextArea rows={3} placeholder="Enter command to execute" />
                  </Form.Item>
                );
              }
              
              if (taskType === 'script') {
                return (
                  <Form.Item
                    name="script_content"
                    label="Script Content"
                    rules={[{ required: true, message: 'Please enter script content' }]}
                  >
                    <TextArea rows={6} placeholder="Enter script content" />
                  </Form.Item>
                );
              }
              
              if (taskType === 'file_transfer') {
                return (
                  <>
                    <Form.Item
                      name="source_path"
                      label="Source Path"
                      rules={[{ required: true, message: 'Please enter source path' }]}
                    >
                      <Input placeholder="Enter source file path" />
                    </Form.Item>
                    
                    <Form.Item
                      name="target_path"
                      label="Target Path"
                      rules={[{ required: true, message: 'Please enter target path' }]}
                    >
                      <Input placeholder="Enter target file path" />
                    </Form.Item>
                  </>
                );
              }
              
              return null;
            }}
          </Form.Item>

          <Form.Item
            name="host_ids"
            label="Target Hosts"
            rules={[{ required: true, message: 'Please select at least one host' }]}
          >
            <Select
              mode="multiple"
              placeholder="Select hosts to execute on"
              optionFilterProp="children"
            >
              {hosts.map(host => (
                <Option key={host.id} value={host.id}>
                  {host.name} ({host.hostname})
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="parallel"
            label="Parallel Execution"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Form>
      </Modal>

      <Drawer
        title="Task Logs"
        width={800}
        open={logDrawerVisible}
        onClose={() => setLogDrawerVisible(false)}
      >
        {selectedTaskId && (
          <TaskLogViewer taskId={selectedTaskId} />
        )}
      </Drawer>
    </div>
  );
};

export default TaskManagement;
