import React, { useState, useEffect, useRef } from "react";
import { Modal, Button, Typography, Space, message, Tabs, Input, Row, Col } from "antd";
import { CloseOutlined, PlusOutlined, MinusOutlined, SendOutlined, ClearOutlined } from "@ant-design/icons";
import { Host } from "../types";
import { sshTerminalApi } from "../services/api";
import { listen, Event } from "@tauri-apps/api/event";

const { Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { TextArea } = Input;

interface SshTerminalProps {
  visible: boolean;
  host: Host | null;
  onClose: () => void;
  onMinimize?: () => void;
}

interface TerminalOutput {
  session_id: string;
  output: string;
}

interface TerminalTab {
  key: string;
  title: string;
  sessionId: string | null;
  command: string;
  output: string;
  history: string[];
}

const SshTerminal: React.FC<SshTerminalProps> = ({
  visible,
  host,
  onClose,
  onMinimize,
}) => {
  const [tabs, setTabs] = useState<TerminalTab[]>([]);
  const [activeTabKey, setActiveTabKey] = useState<string>("");
  const outputRefs = useRef<Record<string, HTMLDivElement | null>>({});
  const tabsRef = useRef(tabs);

  useEffect(() => {
    tabsRef.current = tabs;
  }, [tabs]);

  const handleSendCommand = (sessionId: string, command: string) => {
    console.log(`[FE] Sending command to session ${sessionId}:`, command);
    if (command.trim()) {
      // 添加命令到历史记录和输出显示
      setTabs(prevTabs =>
        prevTabs.map(tab =>
          tab.key === activeTabKey && tab.sessionId === sessionId
            ? {
                ...tab,
                output: tab.output + `root@debian:~# ${command}\n`,
                history: [...tab.history, command]
              }
            : tab
        )
      );

      // 发送命令到后端
      sshTerminalApi.sendCommand(sessionId, command + '\r');
    }
  };

  const updateTabCommand = (key: string, command: string) => {
    setTabs(prevTabs =>
      prevTabs.map(tab =>
        tab.key === key ? { ...tab, command } : tab
      )
    );
  };

  const clearTabOutput = (key: string) => {
    setTabs(prevTabs =>
      prevTabs.map(tab =>
        tab.key === key ? { ...tab, output: '', history: [] } : tab
      )
    );
  };

  useEffect(() => {
    const setupOutputListener = async () => {
      return listen<TerminalOutput>("ssh-terminal-output", (event: Event<TerminalOutput>) => {
        const { session_id, output } = event.payload;
        console.log(`[FE] Received data from session ${session_id}:`, output);

        // 更新对应tab的输出
        setTabs(prevTabs =>
          prevTabs.map(tab =>
            tab.sessionId === session_id
              ? { ...tab, output: tab.output + output }
              : tab
          )
        );
      });
    };

    let unlisten: (() => void) | undefined;
    setupOutputListener().then(unlistenFn => {
      unlisten = unlistenFn;
    });

    return () => {
      if (unlisten) {
        console.log("[FE] Unlistening from ssh-terminal-output");
        unlisten();
      }
    };
  }, []);

  const createNewTab = async (host: Host) => {
    const tabKey = `tab-${Date.now()}`;
    console.log(`[FE] Creating new tab: ${tabKey} for host: ${host.hostname}`);
    
    const newTab: TerminalTab = {
      key: tabKey,
      title: `${host.hostname}`,
      sessionId: null,
      command: "",
      output: "",
      history: [],
    };

    setTabs((prev) => [...prev, newTab]);
    setActiveTabKey(tabKey);

    // 更新输出显示连接状态
    setTabs((prev) =>
      prev.map((tab) =>
        tab.key === tabKey ? { ...tab, output: "Connecting...\n" } : tab
      )
    );

    try {
      const newSessionId = await sshTerminalApi.createSession(host.id);
      console.log(`[FE] Session created with ID: ${newSessionId}`);

      // 移除键盘输入监听，改用命令输入框

      setTabs((prev) =>
        prev.map((tab) =>
          tab.key === tabKey ? { ...tab, sessionId: newSessionId } : tab
        )
      );
      message.success("SSH connection successful");
    } catch (error) {
      console.error("[FE] Error creating SSH session:", error);
      setTabs((prev) =>
        prev.map((tab) =>
          tab.key === tabKey ? { ...tab, output: tab.output + `\nConnection failed: ${error}\n` } : tab
        )
      );
      message.error("SSH connection failed");
    }
  };

  const closeTab = async (targetKey: string) => {
    const tabToClose = tabs.find((tab) => tab.key === targetKey);
    if (!tabToClose) return;

    console.log(`[FE] Closing tab: ${targetKey}, session: ${tabToClose.sessionId}`);
    if (tabToClose.sessionId) {
      try {
        await sshTerminalApi.closeSession(tabToClose.sessionId);
        console.log(`[FE] Session ${tabToClose.sessionId} closed successfully.`);
      } catch (error) {
        console.error(`[FE] Failed to close SSH session ${tabToClose.sessionId}:`, error);
      }
    }
    // 不需要dispose，因为我们不再使用xterm

    const newTabs = tabs.filter((tab) => tab.key !== targetKey);
    setTabs(newTabs);

    if (activeTabKey === targetKey && newTabs.length > 0) {
      setActiveTabKey(newTabs[newTabs.length - 1].key);
    } else if (newTabs.length === 0) {
      onClose();
    }
  };

  const handleClose = () => {
    console.log("[FE] Close button clicked, closing all tabs.");
    tabs.forEach(tab => closeTab(tab.key));
    onClose();
  };

  useEffect(() => {
    if (visible && host && tabs.length === 0) {
      createNewTab(host);
    }
    if (!visible && tabs.length > 0) {
        handleClose();
    }
  }, [visible, host]);

  useEffect(() => {
    const activeTab = tabs.find(tab => tab.key === activeTabKey);
    const terminalContainer = terminalRefs.current[activeTabKey];

    if (activeTab && terminalContainer && !activeTab.terminal.element) {
      activeTab.terminal.open(terminalContainer);
      activeTab.fitAddon.fit();

      const resizeObserver = new ResizeObserver(() => {
        try {
          activeTab.fitAddon.fit();
        } catch (e) {
          // This can happen if the terminal is disposed, ignore
        }
      });
      resizeObserver.observe(terminalContainer);

      return () => resizeObserver.disconnect();
    }
  }, [activeTabKey, tabs]);

  return (
    <Modal
      title={
        <div style={{ display: "flex", justifyContent: "space-between", cursor: "move" }} className="draggable-handle">
          <Text strong>SSH Terminal - {host?.hostname}</Text>
          <Space>
            <Button size="small" icon={<PlusOutlined />} onClick={() => host && createNewTab(host)} disabled={!host}>New</Button>
            <Button size="small" icon={<MinusOutlined />} onClick={onMinimize}>Minimize</Button>
            <Button size="small" icon={<CloseOutlined />} onClick={handleClose} danger>Close</Button>
          </Space>
        </div>
      }
      open={visible}
      onCancel={handleClose}
      footer={null}
      width={800}
      style={{ top: 20 }}
      bodyStyle={{ padding: 0, height: "600px", overflow: "hidden" }}
      destroyOnClose
      mask={false}
      modalRender={(modal) => <div className="ssh-terminal-modal">{modal}</div>}
    >
      <Tabs
        type="editable-card"
        activeKey={activeTabKey}
        onChange={setActiveTabKey}
        onEdit={(targetKey, action) => {
          if (action === "remove") closeTab(targetKey as string);
        }}
        hideAdd
      >
        {tabs.map((tab) => (
          <TabPane tab={tab.title} key={tab.key} closable={tabs.length > 1}>
            <div style={{ height: "560px", display: "flex", flexDirection: "column" }}>
              {/* 终端输出区域 */}
              <div
                ref={(el) => (terminalRefs.current[tab.key] = el)}
                style={{
                  flex: 1,
                  backgroundColor: "#000080", // Borland深蓝色背景
                  padding: "5px",
                  marginBottom: "8px",
                  border: "1px solid #0000FF",
                  borderRadius: "4px"
                }}
              />
              {/* 命令输入区域 */}
              <Row gutter={8} style={{ padding: "0 8px 8px 8px" }}>
                <Col flex="auto">
                  <Input
                    placeholder="输入命令..."
                    value={tab.command}
                    onChange={(e) => updateTabCommand(tab.key, e.target.value)}
                    onPressEnter={() => {
                      if (tab.sessionId) {
                        handleSendCommand(tab.sessionId, tab.command);
                        updateTabCommand(tab.key, "");
                      }
                    }}
                    disabled={!tab.sessionId}
                  />
                </Col>
                <Col>
                  <Button
                    type="primary"
                    icon={<SendOutlined />}
                    onClick={() => {
                      if (tab.sessionId) {
                        handleSendCommand(tab.sessionId, tab.command);
                        updateTabCommand(tab.key, "");
                      }
                    }}
                    disabled={!tab.sessionId || !tab.command.trim()}
                  >
                    发送
                  </Button>
                </Col>
              </Row>
            </div>
          </TabPane>
        ))}
      </Tabs>
    </Modal>
  );
};

export default SshTerminal;
