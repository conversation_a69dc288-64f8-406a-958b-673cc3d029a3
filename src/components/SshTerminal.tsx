import React, { useState, useEffect, useRef, useMemo } from "react";
import { Modal, Button, Typography, Space, message, Tabs, Input, Row, Col } from "antd";
import { CloseOutlined, PlusOutlined, MinusOutlined, SendOutlined } from "@ant-design/icons";
import { Host } from "../types";
import { sshTerminalApi } from "../services/api";
import { listen, Event } from "@tauri-apps/api/event";
import { Terminal } from "xterm";
import { FitAddon } from "xterm-addon-fit";
import "xterm/css/xterm.css";

const { Text } = Typography;
const { TabPane } = Tabs;

interface SshTerminalProps {
  visible: boolean;
  host: Host | null;
  onClose: () => void;
  onMinimize?: () => void;
}

interface TerminalOutput {
  session_id: string;
  output: string;
}

interface TerminalTab {
  key: string;
  title: string;
  terminal: Terminal;
  fitAddon: FitAddon;
  sessionId: string | null;
  command: string;
}

const SshTerminal: React.FC<SshTerminalProps> = ({
  visible,
  host,
  onClose,
  onMinimize,
}) => {
  const [tabs, setTabs] = useState<TerminalTab[]>([]);
  const [activeTabKey, setActiveTabKey] = useState<string>("");
  const terminalRefs = useRef<Record<string, HTMLDivElement | null>>({});
  const tabsRef = useRef(tabs);

  useEffect(() => {
    tabsRef.current = tabs;
  }, [tabs]);

  const handleSendCommand = (sessionId: string, command: string) => {
    console.log(`[FE] Sending command to session ${sessionId}:`, command);
    if (command.trim()) {
      // 添加回车符执行命令
      sshTerminalApi.sendCommand(sessionId, command + '\r');
    }
  };

  const updateTabCommand = (key: string, command: string) => {
    setTabs(prevTabs =>
      prevTabs.map(tab =>
        tab.key === key ? { ...tab, command } : tab
      )
    );
  };

  useEffect(() => {
    const setupOutputListener = async () => {
      return listen<TerminalOutput>("ssh-terminal-output", (event: Event<TerminalOutput>) => {
        const { session_id, output } = event.payload;
        console.log(`[FE] Received data from session ${session_id}:`, output);
        const tab = tabsRef.current.find((t) => t.sessionId === session_id);
        if (tab?.terminal) {
          tab.terminal.write(output);
        } else {
          console.warn(`[FE] Received data for non-existent tab/session: ${session_id}`);
        }
      });
    };

    let unlisten: (() => void) | undefined;
    setupOutputListener().then(unlistenFn => {
      unlisten = unlistenFn;
    });

    return () => {
      if (unlisten) {
        console.log("[FE] Unlistening from ssh-terminal-output");
        unlisten();
      }
    };
  }, []);

  const createNewTab = async (host: Host) => {
    const tabKey = `tab-${Date.now()}`;
    console.log(`[FE] Creating new tab: ${tabKey} for host: ${host.hostname}`);
    
    const term = new Terminal({
      cursorBlink: true,
      convertEol: true,
      fontSize: 14,
      fontFamily: "Menlo, Monaco, 'Courier New', monospace",
    });
    const fitAddon = new FitAddon();
    term.loadAddon(fitAddon);

    const newTab: TerminalTab = {
      key: tabKey,
      title: `${host.hostname}`,
      terminal: term,
      fitAddon,
      sessionId: null,
      command: "",
    };

    setTabs((prev) => [...prev, newTab]);
    setActiveTabKey(tabKey);

    term.write("Connecting...\r\n");

    try {
      const newSessionId = await sshTerminalApi.createSession(host.id);
      console.log(`[FE] Session created with ID: ${newSessionId}`);
      
      // 移除键盘输入监听，改用命令输入框
      
      setTabs((prev) =>
        prev.map((tab) =>
          tab.key === tabKey ? { ...tab, sessionId: newSessionId } : tab
        )
      );
      message.success("SSH connection successful");
    } catch (error) {
      console.error("[FE] Error creating SSH session:", error);
      term.write(`\r\nConnection failed: ${error}\r\n`);
      message.error("SSH connection failed");
    }
  };

  const closeTab = async (targetKey: string) => {
    const tabToClose = tabs.find((tab) => tab.key === targetKey);
    if (!tabToClose) return;

    console.log(`[FE] Closing tab: ${targetKey}, session: ${tabToClose.sessionId}`);
    if (tabToClose.sessionId) {
      try {
        await sshTerminalApi.closeSession(tabToClose.sessionId);
        console.log(`[FE] Session ${tabToClose.sessionId} closed successfully.`);
      } catch (error) {
        console.error(`[FE] Failed to close SSH session ${tabToClose.sessionId}:`, error);
      }
    }
    tabToClose.terminal.dispose();

    const newTabs = tabs.filter((tab) => tab.key !== targetKey);
    setTabs(newTabs);

    if (activeTabKey === targetKey && newTabs.length > 0) {
      setActiveTabKey(newTabs[newTabs.length - 1].key);
    } else if (newTabs.length === 0) {
      onClose();
    }
  };

  const handleClose = () => {
    console.log("[FE] Close button clicked, closing all tabs.");
    tabs.forEach(tab => closeTab(tab.key));
    onClose();
  };

  useEffect(() => {
    if (visible && host && tabs.length === 0) {
      createNewTab(host);
    }
    if (!visible && tabs.length > 0) {
        handleClose();
    }
  }, [visible, host]);

  useEffect(() => {
    const activeTab = tabs.find(tab => tab.key === activeTabKey);
    const terminalContainer = terminalRefs.current[activeTabKey];

    if (activeTab && terminalContainer && !activeTab.terminal.element) {
      activeTab.terminal.open(terminalContainer);
      activeTab.fitAddon.fit();

      const resizeObserver = new ResizeObserver(() => {
        try {
          activeTab.fitAddon.fit();
        } catch (e) {
          // This can happen if the terminal is disposed, ignore
        }
      });
      resizeObserver.observe(terminalContainer);

      return () => resizeObserver.disconnect();
    }
  }, [activeTabKey, tabs]);

  return (
    <Modal
      title={
        <div style={{ display: "flex", justifyContent: "space-between", cursor: "move" }} className="draggable-handle">
          <Text strong>SSH Terminal - {host?.hostname}</Text>
          <Space>
            <Button size="small" icon={<PlusOutlined />} onClick={() => host && createNewTab(host)} disabled={!host}>New</Button>
            <Button size="small" icon={<MinusOutlined />} onClick={onMinimize}>Minimize</Button>
            <Button size="small" icon={<CloseOutlined />} onClick={handleClose} danger>Close</Button>
          </Space>
        </div>
      }
      open={visible}
      onCancel={handleClose}
      footer={null}
      width={800}
      style={{ top: 20 }}
      bodyStyle={{ padding: 0, height: "600px", overflow: "hidden" }}
      destroyOnClose
      mask={false}
      modalRender={(modal) => <div className="ssh-terminal-modal">{modal}</div>}
    >
      <Tabs
        type="editable-card"
        activeKey={activeTabKey}
        onChange={setActiveTabKey}
        onEdit={(targetKey, action) => {
          if (action === "remove") closeTab(targetKey as string);
        }}
        hideAdd
      >
        {tabs.map((tab) => (
          <TabPane tab={tab.title} key={tab.key} closable={tabs.length > 1}>
            <div style={{ height: "560px", display: "flex", flexDirection: "column" }}>
              {/* 终端输出区域 */}
              <div
                ref={(el) => (terminalRefs.current[tab.key] = el)}
                style={{
                  flex: 1,
                  backgroundColor: "#1e1e1e",
                  padding: "5px",
                  marginBottom: "8px"
                }}
              />
              {/* 命令输入区域 */}
              <Row gutter={8} style={{ padding: "0 8px 8px 8px" }}>
                <Col flex="auto">
                  <Input
                    placeholder="输入命令..."
                    value={tab.command}
                    onChange={(e) => updateTabCommand(tab.key, e.target.value)}
                    onPressEnter={() => {
                      if (tab.sessionId) {
                        handleSendCommand(tab.sessionId, tab.command);
                        updateTabCommand(tab.key, "");
                      }
                    }}
                    disabled={!tab.sessionId}
                  />
                </Col>
                <Col>
                  <Button
                    type="primary"
                    icon={<SendOutlined />}
                    onClick={() => {
                      if (tab.sessionId) {
                        handleSendCommand(tab.sessionId, tab.command);
                        updateTabCommand(tab.key, "");
                      }
                    }}
                    disabled={!tab.sessionId || !tab.command.trim()}
                  >
                    发送
                  </Button>
                </Col>
              </Row>
            </div>
          </TabPane>
        ))}
      </Tabs>
    </Modal>
  );
};

export default SshTerminal;
