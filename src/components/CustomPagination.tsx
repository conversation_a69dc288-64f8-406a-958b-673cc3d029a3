import React from 'react';
import { TablePaginationConfig } from 'antd';

interface CustomPaginationProps {
  total: number;
  current?: number;
  pageSize?: number;
  onChange?: (page: number, pageSize?: number) => void;
  onShowSizeChange?: (current: number, size: number) => void;
}

export const createCustomPagination = (props: CustomPaginationProps): TablePaginationConfig => {
  const { total, current = 1, pageSize = 10, onChange, onShowSizeChange } = props;

  // 创建分页选项，包括"显示全部"
  const pageSizeOptions = ['10', '20', '50', '100', '500', '1000'];

  // 总是添加"显示全部"选项
  if (total > 0) {
    // 添加一个足够大的数字来表示"显示全部"
    const allOption = Math.max(total, 10000).toString();
    pageSizeOptions.push(allOption);
  }

  return {
    current,
    pageSize,
    total,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total, range) => {
      if (pageSize >= total) {
        return `显示全部 ${total} 条`;
      }
      return `${range[0]}-${range[1]} / ${total} 条`;
    },
    pageSizeOptions,
    showLessItems: true,
    onChange: (page, size) => {
      if (onChange) {
        onChange(page, size);
      }
    },
    onShowSizeChange: (current, size) => {
      // 处理"显示全部"的情况
      let actualSize = size;
      if (size >= total) {
        actualSize = total;
      }

      if (onShowSizeChange) {
        onShowSizeChange(current, actualSize);
      }
    },
    itemRender: (current, type, originalElement) => {
      if (type === 'page') {
        return <a>{current}</a>;
      }
      return originalElement;
    },
  };
};

export default createCustomPagination;
