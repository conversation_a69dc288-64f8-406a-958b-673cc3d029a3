import React, { useMemo, useRef, useEffect, useState } from 'react';
import { FixedSizeList as List } from 'react-window';
import { Table, TableProps } from 'antd';
import { ColumnsType } from 'antd/es/table';

interface VirtualizedTableProps<T> extends Omit<TableProps<T>, 'pagination'> {
  height?: number;
  itemHeight?: number;
}

interface RowData<T> {
  index: number;
  data: T[];
  columns: ColumnsType<T>;
  rowSelection?: TableProps<T>['rowSelection'];
  onRow?: TableProps<T>['onRow'];
}

const VirtualizedRow = <T extends Record<string, any>>({
  index,
  style,
  data,
}: {
  index: number;
  style: React.CSSProperties;
  data: RowData<T>;
}) => {
  const { data: tableData, columns, rowSelection, onRow } = data;
  const record = tableData[index];

  if (!record) {
    return <div style={style} />;
  }

  const isSelected = rowSelection?.selectedRowKeys?.includes(record.key || record.id);
  const rowProps = onRow?.(record, index) || {};

  return (
    <div
      style={{
        ...style,
        display: 'flex',
        alignItems: 'center',
        borderBottom: '1px solid #f0f0f0',
        backgroundColor: isSelected ? '#e6f7ff' : '#fff',
        cursor: 'pointer',
        padding: '8px 0',
      }}
      {...rowProps}
      onClick={(e) => {
        if (rowSelection?.onSelect) {
          rowSelection.onSelect(record, !isSelected, tableData);
        }
        rowProps.onClick?.(e);
      }}
    >
      {rowSelection && (
        <div style={{ width: '50px', textAlign: 'center', flexShrink: 0 }}>
          <input
            type="checkbox"
            checked={isSelected}
            onChange={(e) => {
              e.stopPropagation();
              if (rowSelection?.onSelect) {
                rowSelection.onSelect(record, e.target.checked, tableData);
              }
            }}
          />
        </div>
      )}
      {columns.map((column, colIndex) => {
        const { dataIndex, key, width, render, align } = column;
        const cellKey = key || dataIndex || colIndex;
        let value = record;

        if (dataIndex) {
          if (Array.isArray(dataIndex)) {
            value = dataIndex.reduce((obj, key) => obj?.[key], record);
          } else {
            value = record[dataIndex];
          }
        }

        const displayValue = render ? render(value, record, index) : value;

        return (
          <div
            key={cellKey}
            style={{
              width: width || 'auto',
              flexShrink: width ? 0 : 1,
              padding: '0 8px',
              textAlign: align || 'left',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {displayValue}
          </div>
        );
      })}
    </div>
  );
};

const VirtualizedTable = <T extends Record<string, any>>({
  dataSource = [],
  columns = [],
  height = 400,
  itemHeight = 54,
  rowSelection,
  onRow,
  ...props
}: VirtualizedTableProps<T>) => {
  const listRef = useRef<List>(null);
  const [headerHeight, setHeaderHeight] = useState(55);
  const headerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (headerRef.current) {
      setHeaderHeight(headerRef.current.offsetHeight);
    }
  }, [columns]);

  const memoizedRowData = useMemo(
    () => ({
      data: dataSource,
      columns,
      rowSelection,
      onRow,
    }),
    [dataSource, columns, rowSelection, onRow]
  );

  const totalWidth = useMemo(() => {
    let width = 0;
    if (rowSelection) width += 50;
    columns.forEach((col) => {
      width += (col.width as number) || 150;
    });
    return width;
  }, [columns, rowSelection]);

  return (
    <div
      style={{
        border: '1px solid #f0f0f0',
        borderRadius: '6px',
        overflow: 'hidden',
      }}
    >
      {/* Table Header */}
      <div
        ref={headerRef}
        style={{
          display: 'flex',
          alignItems: 'center',
          backgroundColor: '#fafafa',
          borderBottom: '1px solid #f0f0f0',
          fontWeight: 600,
          padding: '12px 0',
          minWidth: totalWidth,
        }}
      >
        {rowSelection && (
          <div style={{ width: '50px', textAlign: 'center', flexShrink: 0 }}>
            <input
              type="checkbox"
              checked={
                dataSource.length > 0 &&
                dataSource.every((item) =>
                  rowSelection.selectedRowKeys?.includes(item.key || item.id)
                )
              }
              indeterminate={
                rowSelection.selectedRowKeys &&
                rowSelection.selectedRowKeys.length > 0 &&
                rowSelection.selectedRowKeys.length < dataSource.length
              }
              onChange={(e) => {
                if (rowSelection?.onSelectAll) {
                  rowSelection.onSelectAll(
                    e.target.checked,
                    e.target.checked ? dataSource : [],
                    e.target.checked ? dataSource : []
                  );
                }
              }}
            />
          </div>
        )}
        {columns.map((column, index) => (
          <div
            key={column.key || column.dataIndex || index}
            style={{
              width: column.width || 'auto',
              flexShrink: column.width ? 0 : 1,
              padding: '0 8px',
              textAlign: column.align || 'left',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {column.title}
          </div>
        ))}
      </div>

      {/* Virtualized Table Body */}
      <div style={{ height: height - headerHeight, minWidth: totalWidth }}>
        {dataSource.length > 0 ? (
          <List
            ref={listRef}
            height={height - headerHeight}
            itemCount={dataSource.length}
            itemSize={itemHeight}
            itemData={memoizedRowData}
            width="100%"
          >
            {VirtualizedRow}
          </List>
        ) : (
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
              color: '#999',
            }}
          >
            暂无数据
          </div>
        )}
      </div>
    </div>
  );
};

export default VirtualizedTable;
