import React, { useState, useEffect, useCallback, memo } from "react";
import {
  Card,
  Row,
  Col,
  Progress,
  Tag,
  Typography,
  Space,
  Switch,
  Tooltip,
} from "antd";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Responsive<PERSON><PERSON>r,
  <PERSON><PERSON><PERSON>,
  Line,
} from "recharts";
import {
  MonitorOutlined,
  DatabaseOutlined,
  WifiOutlined,
  HddOutlined,
} from "@ant-design/icons";
import { usePerformanceMonitor, useCache } from "../hooks/usePerformance";

const { Title, Text } = Typography;

interface PerformanceMetrics {
  timestamp: number;
  cpuUsage: number;
  memoryUsage: number;
  networkIn: number;
  networkOut: number;
  activeConnections: number;
  responseTime: number;
  errorRate: number;
}

interface ResourceStats {
  totalMemory: number;
  usedMemory: number;
  activeSessions: number;
  totalRequests: number;
  avgResponseTime: number;
  uptime: number;
}

interface PerformanceMonitorProps {
  visible?: boolean;
  refreshInterval?: number;
  onMetricsUpdate?: (metrics: PerformanceMetrics) => void;
}

const PerformanceMonitor: React.FC<PerformanceMonitorProps> = memo(
  ({ visible = true, refreshInterval = 5000, onMetricsUpdate }) => {
    const [metrics, setMetrics] = useState<PerformanceMetrics[]>([]);
    const [currentStats, setCurrentStats] = useState<ResourceStats | null>(
      null,
    );
    const [isMonitoring, setIsMonitoring] = useState(true);
    const [alertThresholds, setAlertThresholds] = useState({
      cpu: 80,
      memory: 85,
      responseTime: 1000,
      errorRate: 5,
    });

    const { start, end, measure } = usePerformanceMonitor("PerformanceMonitor");

    // 模拟获取性能数据的函数
    const fetchPerformanceData =
      useCallback(async (): Promise<PerformanceMetrics> => {
        return new Promise((resolve) => {
          setTimeout(() => {
            const now = Date.now();
            const mockData: PerformanceMetrics = {
              timestamp: now,
              cpuUsage: Math.random() * 100,
              memoryUsage: 60 + Math.random() * 30,
              networkIn: Math.random() * 1000,
              networkOut: Math.random() * 800,
              activeConnections: Math.floor(Math.random() * 50) + 10,
              responseTime: Math.random() * 500 + 100,
              errorRate: Math.random() * 10,
            };
            resolve(mockData);
          }, 100);
        });
      }, []);

    // 模拟获取资源统计数据
    const fetchResourceStats = useCallback(async (): Promise<ResourceStats> => {
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockStats: ResourceStats = {
            totalMemory: 8192,
            usedMemory: 4096 + Math.random() * 2048,
            activeSessions: Math.floor(Math.random() * 20) + 5,
            totalRequests: Math.floor(Math.random() * 10000) + 50000,
            avgResponseTime: Math.random() * 300 + 150,
            uptime: Date.now() - Math.random() * 86400000, // Random uptime up to 24 hours
          };
          resolve(mockStats);
        }, 50);
      });
    }, []);

    // 使用缓存获取资源统计
    const { data: resourceStats } = useCache(
      "resource-stats",
      fetchResourceStats,
      refreshInterval,
    );

    // 定期更新性能指标
    useEffect(() => {
      if (!isMonitoring || !visible) return;

      const updateMetrics = async () => {
        try {
          const newMetric = await measure(fetchPerformanceData);

          setMetrics((prev) => {
            const updated = [...prev, newMetric].slice(-50); // 保留最近50个数据点
            return updated;
          });

          if (onMetricsUpdate) {
            onMetricsUpdate(newMetric);
          }
        } catch (error) {
          console.error("Failed to fetch performance metrics:", error);
        }
      };

      updateMetrics(); // 立即执行一次
      const interval = setInterval(updateMetrics, refreshInterval);

      return () => clearInterval(interval);
    }, [
      isMonitoring,
      visible,
      refreshInterval,
      fetchPerformanceData,
      measure,
      onMetricsUpdate,
    ]);

    // 更新资源统计
    useEffect(() => {
      if (resourceStats) {
        setCurrentStats(resourceStats);
      }
    }, [resourceStats]);

    // 获取状态颜色
    const getStatusColor = useCallback(
      (value: number, threshold: number, inverse = false) => {
        if (inverse) {
          return value > threshold
            ? "#52c41a"
            : value > threshold * 0.7
              ? "#faad14"
              : "#f5222d";
        }
        return value > threshold
          ? "#f5222d"
          : value > threshold * 0.7
            ? "#faad14"
            : "#52c41a";
      },
      [],
    );

    // 格式化数字
    const formatNumber = useCallback((num: number, decimals = 1) => {
      return num.toFixed(decimals);
    }, []);

    // 格式化字节
    const formatBytes = useCallback(
      (bytes: number) => {
        const sizes = ["B", "KB", "MB", "GB"];
        if (bytes === 0) return "0 B";
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return `${formatNumber(bytes / Math.pow(1024, i))} ${sizes[i]}`;
      },
      [formatNumber],
    );

    // 格式化运行时间
    const formatUptime = useCallback((milliseconds: number) => {
      const seconds = Math.floor(milliseconds / 1000);
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const remainingSeconds = seconds % 60;

      if (hours > 0) {
        return `${hours}h ${minutes}m ${remainingSeconds}s`;
      } else if (minutes > 0) {
        return `${minutes}m ${remainingSeconds}s`;
      } else {
        return `${remainingSeconds}s`;
      }
    }, []);

    const currentMetric = metrics[metrics.length - 1];

    if (!visible) return null;

    return (
      <div
        style={{
          padding: "16px",
          backgroundColor: "#f5f5f5",
          minHeight: "100vh",
        }}
      >
        <Row gutter={[16, 16]} style={{ marginBottom: "16px" }}>
          <Col span={24}>
            <Card size="small">
              <Space
                size="large"
                style={{ width: "100%", justifyContent: "space-between" }}
              >
                <Space>
                  <MonitorOutlined
                    style={{ fontSize: "20px", color: "#1890ff" }}
                  />
                  <Title level={4} style={{ margin: 0 }}>
                    性能监控
                  </Title>
                </Space>
                <Space>
                  <Text>实时监控</Text>
                  <Switch checked={isMonitoring} onChange={setIsMonitoring} />
                </Space>
              </Space>
            </Card>
          </Col>
        </Row>

        {/* 实时指标卡片 */}
        <Row gutter={[16, 16]} style={{ marginBottom: "16px" }}>
          <Col xs={24} sm={12} lg={6}>
            <Card size="small">
              <Space direction="vertical" style={{ width: "100%" }}>
                <Space>
                  <MonitorOutlined style={{ color: "#722ed1" }} />
                  <Text strong>CPU使用率</Text>
                </Space>
                <Progress
                  percent={
                    currentMetric
                      ? Number(formatNumber(currentMetric.cpuUsage))
                      : 0
                  }
                  strokeColor={
                    currentMetric
                      ? getStatusColor(
                          currentMetric.cpuUsage,
                          alertThresholds.cpu,
                        )
                      : "#1890ff"
                  }
                  size="small"
                />
                <Text type="secondary">
                  {currentMetric
                    ? `${formatNumber(currentMetric.cpuUsage)}%`
                    : "0%"}
                </Text>
              </Space>
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={6}>
            <Card size="small">
              <Space direction="vertical" style={{ width: "100%" }}>
                <Space>
                  <HddOutlined style={{ color: "#13c2c2" }} />
                  <Text strong>内存使用率</Text>
                </Space>
                <Progress
                  percent={
                    currentMetric
                      ? Number(formatNumber(currentMetric.memoryUsage))
                      : 0
                  }
                  strokeColor={
                    currentMetric
                      ? getStatusColor(
                          currentMetric.memoryUsage,
                          alertThresholds.memory,
                        )
                      : "#1890ff"
                  }
                  size="small"
                />
                <Text type="secondary">
                  {currentStats
                    ? `${formatBytes(currentStats.usedMemory * 1024 * 1024)} / ${formatBytes(currentStats.totalMemory * 1024 * 1024)}`
                    : "0 MB / 0 MB"}
                </Text>
              </Space>
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={6}>
            <Card size="small">
              <Space direction="vertical" style={{ width: "100%" }}>
                <Space>
                  <WifiOutlined style={{ color: "#52c41a" }} />
                  <Text strong>活跃连接</Text>
                </Space>
                <Text style={{ fontSize: "24px", fontWeight: "bold" }}>
                  {currentMetric ? currentMetric.activeConnections : 0}
                </Text>
                <Text type="secondary">SSH会话</Text>
              </Space>
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={6}>
            <Card size="small">
              <Space direction="vertical" style={{ width: "100%" }}>
                <Space>
                  <DatabaseOutlined style={{ color: "#fa8c16" }} />
                  <Text strong>响应时间</Text>
                </Space>
                <Text style={{ fontSize: "24px", fontWeight: "bold" }}>
                  {currentMetric
                    ? `${formatNumber(currentMetric.responseTime)}ms`
                    : "0ms"}
                </Text>
                <Tag
                  color={
                    currentMetric &&
                    currentMetric.responseTime > alertThresholds.responseTime
                      ? "red"
                      : "green"
                  }
                >
                  {currentMetric &&
                  currentMetric.responseTime > alertThresholds.responseTime
                    ? "慢"
                    : "正常"}
                </Tag>
              </Space>
            </Card>
          </Col>
        </Row>

        {/* 图表区域 */}
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card title="CPU & 内存使用率" size="small">
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={metrics}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="timestamp"
                    tickFormatter={(timestamp) =>
                      new Date(timestamp).toLocaleTimeString()
                    }
                  />
                  <YAxis />
                  <Tooltip
                    labelFormatter={(timestamp: any) =>
                      new Date(timestamp).toLocaleString()
                    }
                    formatter={(value: number, name: string) => [
                      `${formatNumber(value)}%`,
                      name === "cpuUsage" ? "CPU使用率" : "内存使用率",
                    ]}
                  />
                  <Area
                    type="monotone"
                    dataKey="cpuUsage"
                    stackId="1"
                    stroke="#722ed1"
                    fill="#722ed1"
                    fillOpacity={0.6}
                  />
                  <Area
                    type="monotone"
                    dataKey="memoryUsage"
                    stackId="2"
                    stroke="#13c2c2"
                    fill="#13c2c2"
                    fillOpacity={0.6}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </Card>
          </Col>

          <Col xs={24} lg={12}>
            <Card title="网络流量" size="small">
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={metrics}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="timestamp"
                    tickFormatter={(timestamp) =>
                      new Date(timestamp).toLocaleTimeString()
                    }
                  />
                  <YAxis />
                  <Tooltip
                    labelFormatter={(timestamp: any) =>
                      new Date(timestamp).toLocaleString()
                    }
                    formatter={(value: number, name: string) => [
                      `${formatNumber(value)} KB/s`,
                      name === "networkIn" ? "入站流量" : "出站流量",
                    ]}
                  />
                  <Line
                    type="monotone"
                    dataKey="networkIn"
                    stroke="#52c41a"
                    strokeWidth={2}
                    dot={false}
                  />
                  <Line
                    type="monotone"
                    dataKey="networkOut"
                    stroke="#1890ff"
                    strokeWidth={2}
                    dot={false}
                  />
                </LineChart>
              </ResponsiveContainer>
            </Card>
          </Col>
        </Row>

        {/* 系统信息 */}
        {currentStats && (
          <Row gutter={[16, 16]} style={{ marginTop: "16px" }}>
            <Col span={24}>
              <Card title="系统信息" size="small">
                <Row gutter={[16, 16]}>
                  <Col xs={24} sm={12} md={6}>
                    <Space direction="vertical" size="small">
                      <Text strong>活跃会话</Text>
                      <Text style={{ fontSize: "18px" }}>
                        {currentStats.activeSessions}
                      </Text>
                    </Space>
                  </Col>
                  <Col xs={24} sm={12} md={6}>
                    <Space direction="vertical" size="small">
                      <Text strong>总请求数</Text>
                      <Text style={{ fontSize: "18px" }}>
                        {currentStats.totalRequests.toLocaleString()}
                      </Text>
                    </Space>
                  </Col>
                  <Col xs={24} sm={12} md={6}>
                    <Space direction="vertical" size="small">
                      <Text strong>平均响应时间</Text>
                      <Text style={{ fontSize: "18px" }}>
                        {formatNumber(currentStats.avgResponseTime)}ms
                      </Text>
                    </Space>
                  </Col>
                  <Col xs={24} sm={12} md={6}>
                    <Space direction="vertical" size="small">
                      <Text strong>运行时间</Text>
                      <Tooltip
                        title={new Date(
                          Date.now() - currentStats.uptime,
                        ).toLocaleString()}
                      >
                        <Text style={{ fontSize: "18px" }}>
                          {formatUptime(currentStats.uptime)}
                        </Text>
                      </Tooltip>
                    </Space>
                  </Col>
                </Row>
              </Card>
            </Col>
          </Row>
        )}
      </div>
    );
  },
);

PerformanceMonitor.displayName = "PerformanceMonitor";

export default PerformanceMonitor;
