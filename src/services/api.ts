import { invoke } from '@tauri-apps/api/core';
import { Host, CreateHostRequest, UpdateHostRequest, Task, CreateTaskRequest, LogEntry, HostStatus, SshTerminalSession, TerminalOutput } from '../types';

// Host management APIs
export const hostApi = {
  getHosts: (): Promise<Host[]> => invoke('get_hosts'),
  getHost: (id: number): Promise<Host | null> => invoke('get_host', { id }),
  // 统一使用批量接口
  createHost: (request: CreateHostRequest): Promise<Host> =>
    invoke('create_hosts_batch', { requests: [request] }).then((hosts) => (hosts as Host[])[0]),
  createHostsBatch: (requests: CreateHostRequest[]): Promise<Host[]> => invoke('create_hosts_batch', { requests }),
  updateHost: (request: UpdateHostRequest): Promise<Host> => invoke('update_host', { request }),
  deleteHost: (id: number): Promise<void> =>
    invoke('delete_hosts_batch', { ids: [id] }).then(() => {}),
  deleteHostsBatch: (ids: number[]): Promise<number[]> => invoke('delete_hosts_batch', { ids }),
  testConnection: (id: number): Promise<boolean> => invoke('test_host_connection', { id }),
};

// Task management APIs
export const taskApi = {
  getTasks: (): Promise<Task[]> => invoke('get_tasks'),
  getTask: (id: number): Promise<Task | null> => invoke('get_task', { id }),
  createTask: (request: CreateTaskRequest): Promise<Task> => invoke('create_task', { request }),
  // 统一使用批量接口
  deleteTask: (id: number): Promise<void> =>
    invoke('delete_tasks_batch', { ids: [id] }).then(() => {}),
  deleteTasksBatch: (ids: number[]): Promise<number[]> => invoke('delete_tasks_batch', { ids }),
  executeTask: (id: number): Promise<void> => invoke('execute_task', { id }),
  cancelTask: (id: number): Promise<void> => invoke('cancel_task', { id }),
  isTaskRunning: (id: number): Promise<boolean> => invoke('is_task_running', { id }),
};

// Log management APIs
export const logApi = {
  getTaskLogs: (taskId: number): Promise<LogEntry[]> => invoke('get_task_logs', { task_id: taskId }),
  clearTaskLogs: (taskId: number): Promise<void> => invoke('clear_task_logs', { task_id: taskId }),
  subscribeTaskLogs: (taskId: number): Promise<void> => invoke('subscribe_task_logs', { task_id: taskId }),
};

// SSH Terminal APIs
export const sshTerminalApi = {
  createSession: (hostId: number): Promise<string> => invoke('create_ssh_terminal_session', { hostId }),
  sendCommand: (sessionId: string, command: string): Promise<void> => invoke('send_ssh_terminal_command', { sessionId, command }),
  closeSession: (sessionId: string): Promise<void> => invoke('close_ssh_terminal_session', { sessionId }),
  browseDirectory: (hostId: number, path: string): Promise<any[]> => invoke('browse_remote_directory', { hostId: hostId, path })
};

// Utility APIs
export const utilApi = {
  getAppDataDir: (): Promise<string> => invoke('get_app_data_dir'),
};

// Ping service APIs
export const pingApi = {
  getHostStatus: (hostId: number): Promise<HostStatus | null> => invoke('get_host_status', { host_id: hostId }),
  getAllHostStatuses: (): Promise<Record<number, HostStatus>> => invoke('get_all_host_statuses'),
  startHostMonitoring: (): Promise<void> => invoke('start_host_monitoring'),
};

export default sshTerminalApi;