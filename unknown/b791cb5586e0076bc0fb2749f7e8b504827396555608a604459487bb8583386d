use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Host {
    pub id: i64,
    pub name: String,
    pub hostname: String,
    pub port: i32,
    pub username: String,
    pub password: Option<String>,
    pub private_key: Option<String>,
    pub description: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HostStatus {
    pub host_id: i64,
    pub status: String, // "online", "offline", "checking"
    pub ping_time: Option<f64>, // ping延迟，毫秒
    pub last_check: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SshTerminalSession {
    pub session_id: String,
    pub host_id: i64,
    pub created_at: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CreateHostRequest {
    pub name: String,
    pub hostname: String,
    pub port: i32,
    pub username: String,
    pub password: Option<String>,
    pub private_key: Option<String>,
    pub description: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateHostRequest {
    pub id: i64,
    pub name: String,
    pub hostname: String,
    pub port: i32,
    pub username: String,
    pub password: Option<String>,
    pub private_key: Option<String>,
    pub description: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Task {
    pub id: i64,
    pub name: String,
    pub task_type: String, // "command", "file_transfer", "script"
    pub command: Option<String>,
    pub script_content: Option<String>,
    pub source_path: Option<String>,
    pub target_path: Option<String>,
    pub host_ids: String, // JSON array of host IDs
    pub status: String, // "pending", "running", "completed", "failed"
    pub parallel: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub started_at: Option<DateTime<Utc>>,
    pub completed_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateTaskRequest {
    pub name: String,
    pub task_type: String,
    pub command: Option<String>,
    pub script_content: Option<String>,
    pub source_path: Option<String>,
    pub target_path: Option<String>,
    pub host_ids: Vec<i64>,
    pub parallel: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct TaskLog {
    pub id: i64,
    pub task_id: i64,
    pub host_id: i64,
    pub log_type: String, // "stdout", "stderr", "info", "error"
    pub content: String,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskExecution {
    pub task_id: i64,
    pub host_id: i64,
    pub status: String,
    pub output: String,
    pub error: Option<String>,
    pub start_time: DateTime<Utc>,
    pub end_time: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogEntry {
    pub id: i64,
    pub task_id: i64,
    pub host_id: i64,
    pub host_name: String,
    pub log_type: String,
    pub content: String,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskStatus {
    pub task_id: i64,
    pub host_id: i64,
    pub host_name: String,
    pub status: String,
    pub progress: f64,
    pub current_step: String,
}
