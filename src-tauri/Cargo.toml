[package]
name = "tauri-appsshmanger"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
edition = "2021"

# 优化配置
[profile.dev]
# 开发时启用部分优化以提升性能
opt-level = 1
debug = true
split-debuginfo = "unpacked"
overflow-checks = true
lto = false
panic = "unwind"
incremental = true
codegen-units = 256

[profile.release]
# 生产构建优化
opt-level = 3
debug = false
split-debuginfo = "packed"
strip = true
lto = "thin"
panic = "abort"
codegen-units = 1
overflow-checks = false

[profile.test]
opt-level = 1
debug = true
overflow-checks = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "tauri_appsshmanger_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
chrono = { version = "0.4.31", features = ["serde"] }
fern = { version = "0.6", features = ["colored"] }
thiserror = "1.0"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
crossbeam = "0.8"
rayon = "1.7"
parking_lot = "0.12"
futures = "0.3"
once_cell = "1.0"
dashmap = "5.0"
broadcast = "0.1"
tauri-plugin-shell = "2"
uuid = { version = "1.0", features = ["v4", "serde"] }
env_logger = "0.10"
log = "0.4"
anyhow = "1.0"
ssh2 = "0.9"
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite", "chrono", "json"] }
tokio = { version = "1", features = ["full"] }
serde_json = "1"
serde = { version = "1", features = ["derive"] }
tauri-plugin-opener = "2"
base64ct = "=1.6.0"
tauri = { version = "2", features = [] }
