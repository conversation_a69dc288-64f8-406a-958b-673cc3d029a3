use chrono::Local;
use fern::colors::{Color, ColoredLevelConfig};
use log::LevelFilter;
use std::fs;
use std::path::PathBuf;
use std::time::Duration;

/// 日志配置常量
const MAX_LOG_FILES: usize = 10;
const MAX_LOG_FILE_SIZE: u64 = 10 * 1024 * 1024; // 10MB

/// 初始化应用日志记录器
pub fn init_logger() -> Result<PathBuf, fern::InitError> {
    // 定义日志颜色配置
    let colors = ColoredLevelConfig::new()
        .info(Color::Green)
        .warn(Color::Yellow)
        .error(Color::Red)
        .debug(Color::Blue)
        .trace(Color::BrightBlack);

    // 获取项目根目录
    let project_root = std::env::current_dir().expect("无法获取当前工作目录");
    let log_dir = project_root.join("log");

    // 创建日志目录
    if !log_dir.exists() {
        fs::create_dir_all(&log_dir).expect("无法创建日志目录");
    }

    // 清理旧日志文件
    cleanup_old_logs(&log_dir);

    // 生成带时间戳的日志文件名
    let timestamp = Local::now().format("%Y%m%d_%H%M%S");
    let log_file_path = log_dir.join(format!("app_{}.log", timestamp));

    // 配置 fern 日志记录器
    fern::Dispatch::new()
        // 控制台日志输出
        .chain(
            fern::Dispatch::new()
                .format(move |out, message, record| {
                    out.finish(format_args!(
                        "{} [{:<5}] [{}] - {}",
                        Local::now().format("%Y-%m-%d %H:%M:%S%.3f"),
                        colors.color(record.level()),
                        record.target(),
                        message
                    ))
                })
                .level(LevelFilter::Debug) // 控制台显示 Debug 及以上级别的日志
                .chain(std::io::stdout()),
        )
        // 文件日志输出
        .chain(
            fern::Dispatch::new()
                .format(move |out, message, record| {
                    out.finish(format_args!(
                        "{} [{:<5}] [{}] - {}",
                        Local::now().format("%Y-%m-%d %H:%M:%S%.3f"),
                        record.level(),
                        record.target(),
                        message
                    ))
                })
                .level(LevelFilter::Debug) // 文件记录 Debug 及以上级别的日志
                .chain(fern::log_file(&log_file_path)?),
        )
        // 全局应用日志记录器
        .apply()?;

    log::info!(
        "Logger initialized. Log file at: {}",
        log_file_path.display()
    );

    Ok(log_file_path)
}

/// 清理旧日志文件
fn cleanup_old_logs(log_dir: &PathBuf) {
    if let Ok(entries) = fs::read_dir(log_dir) {
        let mut log_files: Vec<_> = entries
            .filter_map(|entry| {
                entry.ok().and_then(|e| {
                    let path = e.path();
                    if path.extension()?.to_str()? == "log" {
                        e.metadata().ok().map(|m| (path, m))
                    } else {
                        None
                    }
                })
            })
            .collect();

        // 按修改时间排序
        log_files.sort_by(|a, b| {
            b.1.modified()
                .unwrap()
                .cmp(&a.1.modified().unwrap())
        });

        // 删除超过最大数量的旧日志文件
        if log_files.len() > MAX_LOG_FILES {
            for (path, _) in log_files[MAX_LOG_FILES..].iter() {
                if let Err(e) = fs::remove_file(path) {
                    eprintln!("Failed to remove old log file {:?}: {}", path, e);
                }
            }
        }
    }
}