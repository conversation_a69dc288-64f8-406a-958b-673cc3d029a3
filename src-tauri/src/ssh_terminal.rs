use std::collections::HashMap;
use std::io::{Read, Write};
use std::sync::{Arc, Mutex};
use std::time::Duration;

use crate::models::Host;
use log::{debug, error, info, warn};
use serde::{Deserialize, Serialize};
use ssh2::{Channel, Session};
use tauri::{AppHandle, Emitter};
use tokio::task::spawn_blocking;
use tokio::time::sleep;
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TerminalOutput {
    pub session_id: String,
    pub output: String,
}

pub struct SshTerminalService {
    sessions: Arc<Mutex<HashMap<String, Arc<Mutex<Channel>>>>>,
    app_handle: Arc<Mutex<Option<AppHandle>>>,
}

impl SshTerminalService {
    pub fn new() -> Self {
        Self {
            sessions: Arc::new(Mutex::new(HashMap::new())),
            app_handle: Arc::new(Mutex::new(None)),
        }
    }

    pub fn set_app_handle(&self, app_handle: AppHandle) {
        *self.app_handle.lock().unwrap() = Some(app_handle);
        info!("SSH terminal service app_handle has been set.");
    }

    pub async fn create_session(&self, host: Host) -> Result<String, String> {
        let session_id = Uuid::new_v4().to_string();
        info!(
            "Creating new SSH session {} for host: {} ({})",
            session_id, host.name, host.hostname
        );

        let host_clone = host.clone();
        let session_id_clone = session_id.clone();
        let channel = spawn_blocking(move || -> Result<Channel, String> {
            let tcp = std::net::TcpStream::connect(format!("{}:{}", host_clone.hostname, host_clone.port))
                .map_err(|e| format!("TCP connection failed: {}", e))?;
            tcp.set_read_timeout(Some(Duration::from_secs(15)))
                .map_err(|e| format!("Failed to set read timeout: {}", e))?;

            let mut sess = Session::new().map_err(|e| format!("Failed to create SSH session: {}", e))?;
            sess.set_tcp_stream(tcp);
            sess.handshake().map_err(|e| format!("SSH handshake failed: {}", e))?;

            if let Some(password) = &host_clone.password {
                sess.userauth_password(&host_clone.username, password)
                    .map_err(|e| format!("Authentication failed: {}", e))?;
            } else {
                return Err("Password authentication is required.".to_string());
            }
            info!("Authentication successful for user '{}'", host_clone.username);

            let mut channel = sess.channel_session().map_err(|e| format!("Failed to open channel: {}", e))?;
            channel.request_pty("xterm-256color", None, None)
                .map_err(|e| format!("Failed to request PTY: {}", e))?;
            channel.shell().map_err(|e| format!("Failed to start shell: {}", e))?;
            
            info!("Shell started for session {}", session_id_clone);
            Ok(channel)
        })
        .await
        .map_err(|e| format!("Task join error: {}", e))??;

        let channel_arc = Arc::new(Mutex::new(channel));
        self.sessions.lock().unwrap().insert(session_id.clone(), channel_arc.clone());

        if let Some(app_handle) = self.app_handle.lock().unwrap().as_ref() {
            let emitter = app_handle.clone();
            let reader_session_id = session_id.clone();
            tokio::spawn(async move {
                let mut buffer = [0; 8192];
                loop {
                    let read_result = channel_arc.lock().unwrap().read(&mut buffer);

                    let bytes_read = match read_result {
                        Ok(n) => n,
                        Err(e) if e.kind() == std::io::ErrorKind::WouldBlock => {
                            sleep(Duration::from_millis(20)).await;
                            continue;
                        }
                        Err(e) => {
                            error!("Error reading from SSH channel for session {}: {}", reader_session_id, e);
                            break;
                        }
                    };

                    if bytes_read == 0 {
                        info!("SSH channel for session {} closed by remote.", reader_session_id);
                        break;
                    }

                    let output = String::from_utf8_lossy(&buffer[..bytes_read]).to_string();
                    debug!("Read {} bytes from session {}: {:?}", bytes_read, reader_session_id, &output);

                    if let Err(e) = emitter.emit("ssh-terminal-output", TerminalOutput {
                        session_id: reader_session_id.clone(),
                        output,
                    }) {
                        error!("Failed to emit ssh-terminal-output event: {}", e);
                    }
                }
                info!("Exiting reader thread for session {}.", reader_session_id);
            });
        }

        Ok(session_id)
    }

    pub async fn send_command(&self, session_id: &str, command: &str) -> Result<(), String> {
        debug!("Attempting to send command to session {}: {:?}", session_id, command);
        let channel_arc = match self.sessions.lock().unwrap().get(session_id) {
            Some(arc) => arc.clone(),
            None => {
                warn!("Attempted to send command to non-existent session: {}", session_id);
                return Err("Session not found".to_string());
            }
        };

        let command_bytes = command.as_bytes().to_vec();
        let session_id_clone = session_id.to_string();
        spawn_blocking(move || {
            let mut channel = channel_arc.lock().unwrap();
            channel.write_all(&command_bytes).map_err(|e| e.to_string())?;
            channel.flush().map_err(|e| e.to_string())?;
            debug!("Successfully sent {} bytes to session {}", command_bytes.len(), session_id_clone);
            Ok(())
        })
        .await
        .map_err(|e| format!("Task join error: {}", e))?
    }

    pub async fn close_session(&self, session_id: &str) -> Result<(), String> {
        info!("Closing SSH session: {}", session_id);
        
        // 1. Lock, remove the item, and then immediately drop the lock
        let channel_arc = self.sessions.lock().unwrap().remove(session_id);

        // 2. Now that the lock is released, perform the blocking operation
        if let Some(arc) = channel_arc {
            let session_id_clone = session_id.to_string();
            spawn_blocking(move || {
                let mut channel = arc.lock().unwrap();
                channel.send_eof().map_err(|e| e.to_string())?;
                channel.close().map_err(|e| e.to_string())?;
                info!("SSH session {} closed successfully.", session_id_clone);
                Ok(())
            })
            .await
            .map_err(|e| format!("Task join error: {}", e))?
        } else {
            warn!("Attempted to close non-existent session: {}", session_id);
            Ok(())
        }
    }
}