use std::process::Command;
use std::time::{Duration, Instant};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use tokio::time::timeout;
use crate::models::{Host, HostStatus};
use chrono::Utc;

pub struct PingService {
    host_statuses: Arc<Mutex<HashMap<i64, HostStatus>>>,
    ping_intervals: Arc<Mutex<HashMap<i64, Duration>>>,
    is_monitoring: Arc<Mutex<bool>>,
}

impl PingService {
    pub fn new() -> Self {
        Self {
            host_statuses: Arc::new(Mutex::new(HashMap::new())),
            ping_intervals: Arc::new(Mutex::new(HashMap::new())),
            is_monitoring: Arc::new(Mutex::new(false)),
        }
    }

    pub async fn start_monitoring(&self, hosts: Vec<Host>) {
        let is_monitoring = {
            let mut monitoring = self.is_monitoring.lock().unwrap();
            if *monitoring {
                return; // Already monitoring
            }
            *monitoring = true;
            true
        };

        if !is_monitoring {
            return;
        }

        // 初始化所有主机状态和ping间隔
        {
            let mut status_map = self.host_statuses.lock().unwrap();
            let mut interval_map = self.ping_intervals.lock().unwrap();

            for host in &hosts {
                status_map.insert(host.id, HostStatus {
                    host_id: host.id,
                    status: "checking".to_string(),
                    ping_time: None,
                    last_check: Utc::now().to_rfc3339(),
                });
                // 初始间隔为30秒
                interval_map.insert(host.id, Duration::from_secs(30));
            }
        }

        // 启动每个主机的独立监控任务
        for host in hosts {
            let statuses = self.host_statuses.clone();
            let intervals = self.ping_intervals.clone();
            let is_monitoring = self.is_monitoring.clone();

            tokio::spawn(async move {
                Self::monitor_host(host, statuses, intervals, is_monitoring).await;
            });
        }
    }

    async fn monitor_host(
        host: Host,
        statuses: Arc<Mutex<HashMap<i64, HostStatus>>>,
        intervals: Arc<Mutex<HashMap<i64, Duration>>>,
        is_monitoring: Arc<Mutex<bool>>,
    ) {
        let mut consecutive_failures = 0;
        let mut consecutive_successes = 0;

        loop {
            // 检查是否还在监控
            {
                let monitoring = is_monitoring.lock().unwrap();
                if !*monitoring {
                    break;
                }
            }

            // 获取当前ping间隔
            let current_interval = {
                let interval_map = intervals.lock().unwrap();
                interval_map.get(&host.id).copied().unwrap_or(Duration::from_secs(30))
            };

            // 执行ping测试
            let ping_result = Self::ping_host_async(&host.hostname).await;

            // 更新状态
            {
                let mut status_map = statuses.lock().unwrap();
                if let Some(status) = status_map.get_mut(&host.id) {
                    match ping_result {
                        Ok(ping_time) => {
                            status.status = "online".to_string();
                            status.ping_time = Some(ping_time);
                            consecutive_failures = 0;
                            consecutive_successes += 1;
                        }
                        Err(_) => {
                            status.status = "offline".to_string();
                            status.ping_time = None;
                            consecutive_failures += 1;
                            consecutive_successes = 0;
                        }
                    }
                    status.last_check = Utc::now().to_rfc3339();
                }
            }

            // 智能调整ping间隔
            let new_interval = Self::calculate_adaptive_interval(
                consecutive_failures,
                consecutive_successes,
                current_interval,
            );

            {
                let mut interval_map = intervals.lock().unwrap();
                interval_map.insert(host.id, new_interval);
            }

            // 等待下次ping
            tokio::time::sleep(new_interval).await;
        }
    }

    /// 计算自适应ping间隔
    fn calculate_adaptive_interval(
        consecutive_failures: u32,
        consecutive_successes: u32,
        current_interval: Duration,
    ) -> Duration {
        const MIN_INTERVAL: Duration = Duration::from_secs(10);
        const MAX_INTERVAL: Duration = Duration::from_secs(300); // 5分钟
        const DEFAULT_INTERVAL: Duration = Duration::from_secs(30);

        if consecutive_failures > 0 {
            // 失败时逐渐增加间隔，但不超过最大值
            let multiplier = (consecutive_failures as f64).min(5.0);
            let new_secs = (current_interval.as_secs() as f64 * (1.0 + multiplier * 0.2)) as u64;
            Duration::from_secs(new_secs.min(MAX_INTERVAL.as_secs()))
        } else if consecutive_successes > 5 {
            // 连续成功时可以减少检查频率
            Duration::from_secs(60) // 1分钟
        } else if consecutive_successes > 0 {
            // 恢复正常间隔
            DEFAULT_INTERVAL
        } else {
            current_interval
        }
    }

    /// 异步ping实现
    async fn ping_host_async(hostname: &str) -> Result<f64, String> {
        let start = Instant::now();

        // 使用超时来避免长时间阻塞
        let ping_timeout = Duration::from_secs(5);

        let result = timeout(ping_timeout, tokio::task::spawn_blocking({
            let hostname = hostname.to_string();
            move || Self::execute_ping(&hostname)
        })).await;

        match result {
            Ok(Ok(Ok(true))) => {
                let duration = start.elapsed();
                Ok(duration.as_millis() as f64)
            }
            Ok(Ok(Ok(false))) => Err("Ping failed".to_string()),
            Ok(Ok(Err(_))) => Err("Ping execution error".to_string()),
            Ok(Err(_)) => Err("Spawn error".to_string()),
            Err(_) => Err("Ping timeout".to_string()),
        }
    }

    /// 执行系统ping命令
    fn execute_ping(hostname: &str) -> Result<bool, std::io::Error> {
        #[cfg(target_os = "windows")]
        let output = Command::new("ping")
            .args(["-n", "1", "-w", "3000", hostname])
            .output()?;

        #[cfg(not(target_os = "windows"))]
        let output = Command::new("ping")
            .args(["-c", "1", "-W", "3", hostname])
            .output()?;

        Ok(output.status.success())
    }

    /// 高性能批量ping
    pub async fn batch_ping(&self, hostnames: Vec<String>) -> HashMap<String, Result<f64, String>> {
        let mut results = HashMap::new();
        let mut tasks = Vec::new();

        // 并行执行所有ping
        for hostname in hostnames {
            let task = tokio::spawn(async move {
                let result = Self::ping_host_async(&hostname).await;
                (hostname, result)
            });
            tasks.push(task);
        }

        // 收集结果
        for task in tasks {
            if let Ok((hostname, result)) = task.await {
                results.insert(hostname, result);
            }
        }

        results
    }

    pub fn get_host_status(&self, host_id: i64) -> Option<HostStatus> {
        let status_map = self.host_statuses.lock().unwrap();
        status_map.get(&host_id).cloned()
    }

    pub fn get_all_statuses(&self) -> HashMap<i64, HostStatus> {
        let status_map = self.host_statuses.lock().unwrap();
        status_map.clone()
    }

    pub fn update_hosts(&self, hosts: Vec<Host>) {
        let mut status_map = self.host_statuses.lock().unwrap();
        let mut interval_map = self.ping_intervals.lock().unwrap();

        // 移除不存在的主机
        let host_ids: std::collections::HashSet<i64> = hosts.iter().map(|h| h.id).collect();
        status_map.retain(|&k, _| host_ids.contains(&k));
        interval_map.retain(|&k, _| host_ids.contains(&k));

        // 添加新主机
        for host in hosts {
            if !status_map.contains_key(&host.id) {
                status_map.insert(host.id, HostStatus {
                    host_id: host.id,
                    status: "checking".to_string(),
                    ping_time: None,
                    last_check: Utc::now().to_rfc3339(),
                });
                interval_map.insert(host.id, Duration::from_secs(30));
            }
        }
    }

    pub fn stop_monitoring(&self) {
        let mut monitoring = self.is_monitoring.lock().unwrap();
        *monitoring = false;
    }

    /// 获取ping统计信息
    pub fn get_ping_stats(&self) -> HashMap<i64, Duration> {
        let interval_map = self.ping_intervals.lock().unwrap();
        interval_map.clone()
    }

    /// 手动触发特定主机的ping检查
    pub async fn manual_ping(&self, host_id: i64, hostname: String) -> Result<f64, String> {
        let result = Self::ping_host_async(&hostname).await;

        // 更新状态
        if let Ok(ping_time) = &result {
            let mut status_map = self.host_statuses.lock().unwrap();
            if let Some(status) = status_map.get_mut(&host_id) {
                status.status = "online".to_string();
                status.ping_time = Some(*ping_time);
                status.last_check = Utc::now().to_rfc3339();
            }
        }

        result
    }
}

impl Default for PingService {
    fn default() -> Self {
        Self::new()
    }
}
