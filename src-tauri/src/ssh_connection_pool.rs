use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use anyhow::{anyhow, Result};
use ssh2::Session;
use tokio::sync::{Mutex, RwLock};
use tokio::time::timeout;
use crate::models::Host;

#[derive(Clone)]
pub struct PooledConnection {
    pub session: Arc<Mutex<Session>>,
    pub host_id: i64,
    pub created_at: Instant,
    pub last_used: Arc<Mutex<Instant>>,
    pub is_active: Arc<Mutex<bool>>,
}

impl PooledConnection {
    pub fn new(session: Session, host_id: i64) -> Self {
        Self {
            session: Arc::new(Mutex::new(session)),
            host_id,
            created_at: Instant::now(),
            last_used: Arc::new(Mutex::new(Instant::now())),
            is_active: Arc::new(Mutex::new(true)),
        }
    }

    pub async fn is_expired(&self, max_idle_time: Duration) -> bool {
        let last_used = *self.last_used.lock().await;
        last_used.elapsed() > max_idle_time
    }

    pub async fn is_connected(&self) -> bool {
        let session = self.session.lock().await;
        session.authenticated()
    }

    pub async fn mark_used(&self) {
        let mut last_used = self.last_used.lock().await;
        *last_used = Instant::now();
    }

    pub async fn close(&self) {
        let mut is_active = self.is_active.lock().await;
        *is_active = false;

        let session = self.session.lock().await;
        let _ = session.disconnect(None, "Connection closed by pool", None);
    }
}

pub struct SshConnectionPool {
    connections: Arc<RwLock<HashMap<i64, Vec<PooledConnection>>>>,
    max_connections_per_host: usize,
    max_idle_time: Duration,
    connection_timeout: Duration,
}

impl SshConnectionPool {
    pub fn new() -> Self {
        Self {
            connections: Arc::new(RwLock::new(HashMap::new())),
            max_connections_per_host: 5,
            max_idle_time: Duration::from_secs(300), // 5 minutes
            connection_timeout: Duration::from_secs(30),
        }
    }

    pub async fn get_connection(&self, host: &Host) -> Result<PooledConnection> {
        // Try to get an existing connection
        if let Some(conn) = self.get_existing_connection(host.id).await? {
            return Ok(conn);
        }

        // Create a new connection
        self.create_new_connection(host).await
    }

    async fn get_existing_connection(&self, host_id: i64) -> Result<Option<PooledConnection>> {
        let mut connections = self.connections.write().await;

        if let Some(host_connections) = connections.get_mut(&host_id) {
            // Find a healthy connection
            let mut _valid_connections: Vec<PooledConnection> = Vec::new();

            let connections_to_check: Vec<_> = host_connections.drain(..).collect();

            for conn in connections_to_check {
                if !conn.is_expired(self.max_idle_time).await && conn.is_connected().await {
                    conn.mark_used().await;

                    // Put back all connections and return this one
                    host_connections.push(conn.clone());
                    return Ok(Some(conn));
                } else {
                    // Close expired/invalid connection
                    conn.close().await;
                }
            }
        }

        Ok(None)
    }

    async fn create_new_connection(&self, host: &Host) -> Result<PooledConnection> {
        // Check if we've reached the limit
        {
            let connections = self.connections.read().await;
            if let Some(host_connections) = connections.get(&host.id) {
                if host_connections.len() >= self.max_connections_per_host {
                    return Err(anyhow!("Maximum connections reached for host {}", host.name));
                }
            }
        }

        // Create new SSH session
        let session = self.create_ssh_session(host).await?;
        let pooled_conn = PooledConnection::new(session, host.id);

        // Add to pool
        {
            let mut connections = self.connections.write().await;
            connections.entry(host.id)
                .or_insert_with(Vec::new)
                .push(pooled_conn.clone());
        }

        Ok(pooled_conn)
    }

    async fn create_ssh_session(&self, host: &Host) -> Result<Session> {
        let tcp = timeout(
            self.connection_timeout,
            tokio::net::TcpStream::connect(format!("{}:{}", host.hostname, host.port)),
        ).await??;

        let tcp = tcp.into_std()?;
        let mut session = Session::new()?;
        session.set_tcp_stream(tcp);
        session.handshake()?;

        // Set timeouts
        session.set_timeout(30000); // 30 seconds

        // Authenticate
        if let Some(private_key) = &host.private_key {
            if !private_key.trim().is_empty() {
                session.userauth_pubkey_memory(&host.username, None, private_key, None)?;
            } else if let Some(password) = &host.password {
                session.userauth_password(&host.username, password)?;
            } else {
                return Err(anyhow!("No authentication method provided"));
            }
        } else if let Some(password) = &host.password {
            session.userauth_password(&host.username, password)?;
        } else {
            return Err(anyhow!("No authentication method available"));
        }

        if !session.authenticated() {
            return Err(anyhow!("Authentication failed"));
        }

        Ok(session)
    }

    pub async fn return_connection(&self, conn: PooledConnection) {
        if conn.is_connected().await {
            conn.mark_used().await;
            // Connection is automatically returned to pool when dropped
        } else {
            conn.close().await;
            self.remove_connection(&conn).await;
        }
    }

    async fn remove_connection(&self, conn: &PooledConnection) {
        let mut connections = self.connections.write().await;
        if let Some(host_connections) = connections.get_mut(&conn.host_id) {
            host_connections.retain(|c| !Arc::ptr_eq(&c.session, &conn.session));
        }
    }

    pub async fn cleanup_expired_connections(&self) {
        let mut connections = self.connections.write().await;

        for (_, host_connections) in connections.iter_mut() {
            let mut valid_connections = Vec::new();

            for conn in host_connections.drain(..) {
                if !conn.is_expired(self.max_idle_time).await && conn.is_connected().await {
                    valid_connections.push(conn);
                } else {
                    conn.close().await;
                }
            }

            *host_connections = valid_connections;
        }

        // Remove empty entries
        connections.retain(|_, v| !v.is_empty());
    }

    pub async fn close_all_connections(&self) {
        let mut connections = self.connections.write().await;

        for (_, host_connections) in connections.iter_mut() {
            for conn in host_connections.iter() {
                conn.close().await;
            }
        }

        connections.clear();
    }

    pub async fn close_host_connections(&self, host_id: i64) {
        let mut connections = self.connections.write().await;

        if let Some(host_connections) = connections.remove(&host_id) {
            for conn in host_connections {
                conn.close().await;
            }
        }
    }

    pub async fn get_pool_stats(&self) -> HashMap<i64, usize> {
        let connections = self.connections.read().await;
        connections.iter()
            .map(|(&host_id, conns)| (host_id, conns.len()))
            .collect()
    }

    // Start cleanup task
    pub fn start_cleanup_task(&self) {
        let pool = self.connections.clone();
        let max_idle_time = self.max_idle_time;

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(60));

            loop {
                interval.tick().await;

                let mut connections = pool.write().await;
                for (_, host_connections) in connections.iter_mut() {
                    let mut valid_connections = Vec::new();

                    for conn in host_connections.drain(..) {
                        if !conn.is_expired(max_idle_time).await && conn.is_connected().await {
                            valid_connections.push(conn);
                        } else {
                            conn.close().await;
                        }
                    }

                    *host_connections = valid_connections;
                }

                connections.retain(|_, v| !v.is_empty());
            }
        });
    }
}

impl Default for SshConnectionPool {
    fn default() -> Self {
        Self::new()
    }
}
