use std::sync::Arc;
use std::io::Read;
use tauri::{State, Emitter};
use log::{debug, info, warn, error};
use crate::database::Database;
use crate::ssh_executor::SshExecutor;
use crate::models::{Host, Task, CreateHostRequest, UpdateHostRequest, CreateTaskRequest, LogEntry, HostStatus};
use crate::ping_service::PingService;
use crate::ssh_terminal::SshTerminalService;


// Application state
pub struct AppState {
    pub db: Arc<Database>,
    pub ssh_executor: Arc<SshExecutor>,
    pub ping_service: Arc<PingService>,
    pub ssh_terminal_service: Arc<SshTerminalService>,
}

// Host management commands
#[tauri::command]
pub async fn create_ssh_terminal_session(state: State<'_, AppState>, hostId: i64) -> Result<String, String> {
    debug!("创建SSH终端会话请求: host_id={}", hostId);
    let host = state.db.get_host_by_id(hostId).await.map_err(|e| {
        error!("获取主机信息失败: host_id={}, error={}", hostId, e);
        e.to_string()
    })?;
    match host {
        Some(host_data) => {
            info!("开始创建SSH终端会话: host_id={}, hostname={}", hostId, host_data.hostname);
            let result = state.ssh_terminal_service.create_session(host_data).await.map_err(|e| {
                error!("创建SSH终端会话失败: host_id={}, error={}", hostId, e);
                e.to_string()
            });
            if result.is_ok() {
                info!("SSH终端会话创建成功: host_id={}", hostId);
            }
            result
        }
        None => {
            warn!("主机不存在: host_id={}", hostId);
            Err("Host not found".to_string())
        }
    }
}

#[tauri::command]
pub async fn close_ssh_terminal_session(state: State<'_, AppState>, sessionId: String) -> Result<(), String> {
    debug!("关闭SSH终端会话请求: session_id={}", sessionId);
    let result = state.ssh_terminal_service.close_session(&sessionId).await.map_err(|e| {
        error!("关闭SSH终端会话失败: session_id={}, error={}", sessionId, e);
        e.to_string()
    });
    if result.is_ok() {
        info!("SSH终端会话关闭成功: session_id={}", sessionId);
    }
    result
}

#[tauri::command]
pub async fn get_hosts(state: State<'_, AppState>) -> Result<Vec<Host>, String> {
    debug!("获取所有主机列表请求");
    let result = state.db.get_all_hosts().await.map_err(|e| {
        error!("获取主机列表失败: error={}", e);
        e.to_string()
    });
    if let Ok(ref hosts) = result {
        debug!("成功获取主机列表: 共{}台主机", hosts.len());
    }
    result
}

#[tauri::command]
pub async fn get_host(state: State<'_, AppState>, id: i64) -> Result<Option<Host>, String> {
    debug!("获取单个主机信息请求: id={}", id);
    let result = state.db.get_host_by_id(id).await.map_err(|e| {
        error!("获取主机信息失败: id={}, error={}", id, e);
        e.to_string()
    });
    if let Ok(Some(ref host)) = result {
        debug!("成功获取主机信息: id={}, name={}", id, host.name);
    } else if let Ok(None) = result {
        warn!("主机不存在: id={}", id);
    }
    result
}

#[tauri::command]
pub async fn create_host(state: State<'_, AppState>, request: CreateHostRequest) -> Result<Host, String> {
    debug!("创建主机请求: name={}, hostname={}, port={}", request.name, request.hostname, request.port);
    let request_name = request.name.clone();
    let result = state.db.create_host(request).await.map_err(|e| {
        error!("创建主机失败: name={}, error={}", request_name, e);
        e.to_string()
    });
    if let Ok(ref host) = result {
        info!("主机创建成功: id={}, name={}, hostname={}", host.id, host.name, host.hostname);
    }
    result
}

#[tauri::command]
pub async fn create_hosts_batch(state: State<'_, AppState>, requests: Vec<CreateHostRequest>) -> Result<Vec<Host>, String> {
    info!("批量创建主机请求: 共{}台主机", requests.len());
    debug!("批量创建主机详情: {:?}", requests);

    match state.db.create_hosts_batch(requests.clone()).await {
        Ok(hosts) => {
            info!("批量创建主机成功: 成功创建{}台主机，共{}台请求", hosts.len(), requests.len());
            Ok(hosts)
        }
        Err(e) => {
            error!("批量创建主机失败: error={}", e);
            Err(e.to_string())
        }
    }
}

#[tauri::command]
pub async fn update_host(state: State<'_, AppState>, request: UpdateHostRequest) -> Result<Host, String> {
    debug!("更新主机请求: id={}, name={}", request.id, request.name);
    let request_id = request.id;
    let result = state.db.update_host(request).await.map_err(|e| {
        error!("更新主机失败: id={}, error={}", request_id, e);
        e.to_string()
    });
    if let Ok(ref host) = result {
        info!("主机更新成功: id={}, name={}, hostname={}", host.id, host.name, host.hostname);
    }
    result
}

#[tauri::command]
pub async fn delete_host(state: State<'_, AppState>, id: i64) -> Result<(), String> {
    debug!("删除主机请求: id={}", id);
    let result = state.db.delete_host(id).await.map_err(|e| {
        error!("删除主机失败: id={}, error={}", id, e);
        e.to_string()
    });
    if result.is_ok() {
        info!("主机删除成功: id={}", id);
    }
    result
}

#[tauri::command]
pub async fn delete_hosts_batch(state: State<'_, AppState>, ids: Vec<i64>) -> Result<Vec<i64>, String> {
    info!("批量删除主机请求: 共{}台主机", ids.len());
    debug!("批量删除主机ID列表: {:?}", ids);

    match state.db.delete_hosts_batch(ids.clone()).await {
        Ok(deleted_ids) => {
            info!("批量删除主机成功: 成功删除{}台主机，共{}台请求", deleted_ids.len(), ids.len());
            debug!("成功删除的主机ID: {:?}", deleted_ids);
            Ok(deleted_ids)
        }
        Err(e) => {
            error!("批量删除主机失败: error={}", e);
            Err(e.to_string())
        }
    }
}

#[tauri::command]
pub async fn test_host_connection(state: State<'_, AppState>, id: i64) -> Result<bool, String> {
    debug!("测试主机连接请求: id={}", id);
    let host = state.db.get_host_by_id(id).await.map_err(|e| {
        error!("获取主机信息失败: id={}, error={}", id, e);
        e.to_string()
    })?;
    if let Some(host) = host {
        info!("开始测试主机连接: id={}, hostname={}, port={}", id, host.hostname, host.port);
        let test_command = "echo 'Connection test successful'";
        match crate::ssh_executor::SshExecutor::create_ssh_session(&host).await {
            Ok(session) => {
                match session.channel_session() {
                    Ok(mut channel) => {
                        match channel.exec(test_command) {
                            Ok(_) => {
                                let _ = channel.wait_close();
                                info!("主机连接测试成功: id={}, hostname={}", id, host.hostname);
                                Ok(true)
                            },
                            Err(e) => {
                                warn!("主机连接测试失败: id={}, hostname={}, error={}", id, host.hostname, e);
                                Ok(false)
                            }
                        }
                    },
                    Err(e) => {
                        warn!("创建SSH通道失败: id={}, hostname={}, error={}", id, host.hostname, e);
                        Ok(false)
                    }
                }
            },
            Err(e) => {
                warn!("创建SSH会话失败: id={}, hostname={}, error={}", id, host.hostname, e);
                Ok(false)
            }
        }
    } else {
        warn!("主机不存在: id={}", id);
        Err("Host not found".to_string())
    }
}

// Task management commands
#[tauri::command]
pub async fn get_tasks(state: State<'_, AppState>) -> Result<Vec<Task>, String> {
    debug!("获取所有任务列表请求");
    let result = state.db.get_all_tasks().await.map_err(|e| {
        error!("获取任务列表失败: error={}", e);
        e.to_string()
    });
    if let Ok(ref tasks) = result {
        debug!("成功获取任务列表: 共{}个任务", tasks.len());
    }
    result
}

#[tauri::command]
pub async fn get_task(state: State<'_, AppState>, id: i64) -> Result<Option<Task>, String> {
    debug!("获取单个任务信息请求: id={}", id);
    let result = state.db.get_task_by_id(id).await.map_err(|e| {
        error!("获取任务信息失败: id={}, error={}", id, e);
        e.to_string()
    });
    if let Ok(Some(ref task)) = result {
        debug!("成功获取任务信息: id={}, name={}", id, task.name);
    } else if let Ok(None) = result {
        warn!("任务不存在: id={}", id);
    }
    result
}

#[tauri::command]
pub async fn create_task(state: State<'_, AppState>, request: CreateTaskRequest) -> Result<Task, String> {
    debug!("创建任务请求: name={}, host_ids={:?}, command={:?}", request.name, request.host_ids, request.command);
    let request_name = request.name.clone();
    let result = state.db.create_task(request).await.map_err(|e| {
        error!("创建任务失败: name={}, error={}", request_name, e);
        e.to_string()
    });
    if let Ok(ref task) = result {
        info!("任务创建成功: id={}, name={}, host_ids={}", task.id, task.name, task.host_ids);
    }
    result
}

#[tauri::command]
pub async fn delete_task(state: State<'_, AppState>, id: i64) -> Result<(), String> {
    debug!("删除任务请求: id={}", id);
    let result = state.db.delete_task(id).await.map_err(|e| {
        error!("删除任务失败: id={}, error={}", id, e);
        e.to_string()
    });
    if result.is_ok() {
        info!("任务删除成功: id={}", id);
    }
    result
}

#[tauri::command]
pub async fn delete_tasks_batch(state: State<'_, AppState>, ids: Vec<i64>) -> Result<Vec<i64>, String> {
    info!("批量删除任务请求: 共{}个任务", ids.len());
    debug!("批量删除任务ID列表: {:?}", ids);

    match state.db.delete_tasks_batch(ids.clone()).await {
        Ok(deleted_ids) => {
            info!("批量删除任务成功: 成功删除{}个任务，共{}个请求", deleted_ids.len(), ids.len());
            debug!("成功删除的任务ID: {:?}", deleted_ids);
            Ok(deleted_ids)
        }
        Err(e) => {
            error!("批量删除任务失败: error={}", e);
            Err(e.to_string())
        }
    }
}

#[tauri::command]
pub async fn execute_task(state: State<'_, AppState>, id: i64) -> Result<(), String> {
    debug!("执行任务请求: id={}", id);
    let executor = state.ssh_executor.clone();
    tokio::spawn(async move {
        info!("开始执行任务: id={}", id);
        if let Err(e) = executor.execute_task(id).await {
            error!("任务执行失败: id={}, error={}", id, e);
        } else {
            info!("任务执行完成: id={}", id);
        }
    });
    info!("任务已提交执行: id={}", id);
    Ok(())
}

#[tauri::command]
pub async fn cancel_task(state: State<'_, AppState>, id: i64) -> Result<(), String> {
    debug!("取消任务请求: id={}", id);
    let result = state.ssh_executor.cancel_task(id).await.map_err(|e| {
        error!("取消任务失败: id={}, error={}", id, e);
        e.to_string()
    });
    if result.is_ok() {
        info!("任务取消成功: id={}", id);
    }
    result
}

#[tauri::command]
pub async fn is_task_running(state: State<'_, AppState>, id: i64) -> Result<bool, String> {
    debug!("查询任务运行状态请求: id={}", id);
    let is_running = state.ssh_executor.is_task_running(id);
    debug!("任务运行状态: id={}, is_running={}", id, is_running);
    Ok(is_running)
}

// Log management commands
#[tauri::command]
pub async fn get_task_logs(state: State<'_, AppState>, task_id: i64) -> Result<Vec<LogEntry>, String> {
    debug!("获取任务日志请求: task_id={}", task_id);
    let result = state.db.get_task_logs(task_id).await.map_err(|e| {
        error!("获取任务日志失败: task_id={}, error={}", task_id, e);
        e.to_string()
    });
    if let Ok(ref logs) = result {
        debug!("成功获取任务日志: task_id={}, 共{}条日志", task_id, logs.len());
    }
    result
}

#[tauri::command]
pub async fn clear_task_logs(state: State<'_, AppState>, task_id: i64) -> Result<(), String> {
    debug!("清空任务日志请求: task_id={}", task_id);
    let result = state.db.clear_task_logs(task_id).await.map_err(|e| {
        error!("清空任务日志失败: task_id={}, error={}", task_id, e);
        e.to_string()
    });
    if result.is_ok() {
        info!("任务日志清空成功: task_id={}", task_id);
    }
    result
}

// Real-time log streaming
#[tauri::command]
pub async fn subscribe_task_logs(
    state: State<'_, AppState>,
    task_id: i64,
    window: tauri::Window,
) -> Result<(), String> {
    debug!("订阅任务日志流请求: task_id={}", task_id);
    if let Some(mut rx) = state.ssh_executor.get_task_log_stream(task_id) {
        info!("开始订阅任务日志流: task_id={}", task_id);
        tokio::spawn(async move {
            while let Ok(log_message) = rx.recv().await {
                debug!("发送任务日志: task_id={}, message={}", task_id, log_message);
                let _ = window.emit("task_log", &log_message);
            }
            info!("任务日志流订阅结束: task_id={}", task_id);
        });
    } else {
        warn!("无法获取任务日志流: task_id={}", task_id);
    }
    Ok(())
}

#[tauri::command]
pub async fn get_app_data_dir() -> Result<String, String> {
    let home_dir = std::env::var("HOME")
        .or_else(|_| std::env::var("USERPROFILE"))
        .map_err(|_| "Failed to get home directory")?;
    let data_dir = format!("{}/.local/share/sshmanger", home_dir);
    Ok(data_dir)
}

// Ping service commands
#[tauri::command]
pub async fn get_host_status(state: State<'_, AppState>, host_id: i64) -> Result<Option<HostStatus>, String> {
    debug!("获取主机状态请求: host_id={}", host_id);
    let status = state.ping_service.get_host_status(host_id);
    debug!("主机状态: host_id={}, status={:?}", host_id, status);
    Ok(status)
}

#[tauri::command]
pub async fn get_all_host_statuses(state: State<'_, AppState>) -> Result<std::collections::HashMap<i64, HostStatus>, String> {
    debug!("获取所有主机状态请求");
    let statuses = state.ping_service.get_all_statuses();
    debug!("成功获取所有主机状态: 共{}台主机", statuses.len());
    Ok(statuses)
}

#[tauri::command]
pub async fn start_host_monitoring(state: State<'_, AppState>) -> Result<(), String> {
    debug!("启动主机监控请求");
    match state.db.get_all_hosts().await {
        Ok(hosts) => {
            info!("开始启动主机监控: 共{}台主机", hosts.len());
            state.ping_service.start_monitoring(hosts).await;
            info!("主机监控启动成功");
            Ok(())
        }
        Err(e) => {
            error!("启动主机监控失败: error={}", e);
            Err(e.to_string())
        }
    }
}

// SSH Terminal commands

#[tauri::command]
pub async fn send_ssh_command(state: State<'_, AppState>, sessionId: String, command: String) -> Result<(), String> {
    debug!("发送SSH命令请求: session_id={}, command={}", sessionId, command);
    let result = state.ssh_terminal_service.send_command(&sessionId, &command).await.map_err(|e| {
        error!("发送SSH命令失败: session_id={}, command={}, error={}", sessionId, command, e);
        e.to_string()
    });
    if result.is_ok() {
        info!("SSH命令发送成功: session_id={}, command={}", sessionId, command);
    }
    result
}

#[tauri::command]
pub async fn close_ssh_session(state: State<'_, AppState>, session_id: String) -> Result<(), String> {
    debug!("关闭SSH会话请求: session_id={}", session_id);
    let result = state.ssh_terminal_service.close_session(&session_id).await.map_err(|e| {
        error!("关闭SSH会话失败: session_id={}, error={}", session_id, e);
        e.to_string()
    });
    if result.is_ok() {
        info!("SSH会话关闭成功: session_id={}", session_id);
    }
    result
}

#[tauri::command]
pub async fn browse_remote_directory(state: State<'_, AppState>, host_id: i64, path: String) -> Result<Vec<serde_json::Value>, String> {
    match state.db.get_host_by_id(host_id).await {
        Ok(Some(host)) => {
            // 使用SSH执行ls命令获取目录内容
            let command = format!("ls -la '{}'", path);
            match crate::ssh_executor::SshExecutor::create_ssh_session(&host).await {
                Ok(session) => {
                    match session.channel_session() {
                        Ok(mut channel) => {
                            match channel.exec(&command) {
                                Ok(_) => {
                                    let mut output = String::new();
                                    match channel.read_to_string(&mut output) {
                                        Ok(_) => {
                                            channel.wait_close().ok();

                                            // 解析ls输出
                                            let mut files = Vec::new();
                                            for line in output.lines().skip(1) { // 跳过第一行 "total xxx"
                                                if let Some(file_info) = parse_ls_line(line, &path) {
                                                    files.push(file_info);
                                                }
                                            }
                                            Ok(files)
                                        }
                                        Err(e) => Err(format!("读取输出失败: {}", e)),
                                    }
                                }
                                Err(e) => Err(format!("执行命令失败: {}", e)),
                            }
                        }
                        Err(e) => Err(format!("创建通道失败: {}", e)),
                    }
                }
                Err(e) => Err(format!("SSH连接失败: {}", e)),
            }
        }
        Ok(None) => Err("Host not found".to_string()),
        Err(e) => Err(e.to_string()),
    }
}

// 解析ls -la输出的单行
fn parse_ls_line(line: &str, parent_path: &str) -> Option<serde_json::Value> {
    let parts: Vec<&str> = line.split_whitespace().collect();
    if parts.len() < 9 {
        return None;
    }

    let permissions = parts[0];
    let name = parts[8..].join(" ");

    // 跳过 . 和 .. 目录
    if name == "." || name == ".." {
        return None;
    }

    let is_directory = permissions.starts_with('d');
    let full_path = if parent_path.ends_with('/') {
        format!("{}{}", parent_path, name)
    } else {
        format!("{}/{}", parent_path, name)
    };

    Some(serde_json::json!({
        "title": name,
        "key": full_path,
        "isLeaf": !is_directory,
        "icon": if is_directory { "folder" } else { "file" },
        "permissions": permissions,
        "size": parts.get(4).unwrap_or(&"0"),
        "modified": parts[5..8].join(" ")
    }))
}
