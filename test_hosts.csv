Name,Hostname,Port,Username,Password,Private Key,Description
Test Server,************,22,root,debian,,Main test environment for SSH Manager
Web Server,*************,22,ubuntu,ubuntu123,,Web application server
Database Server,*************,22,postgres,postgres456,,PostgreSQL database server
Development Server,*************,22,dev,dev789,,Development environment
Staging Server,*************,22,staging,staging123,,Staging environment for testing
Production Server,*************,22,prod,prod456,,Production server - handle with care
Backup Server,*************,22,backup,backup789,,Backup and recovery server
Monitoring Server,*************,22,monitor,monitor123,,System monitoring and alerts
Load Balancer,*************,22,nginx,nginx456,,Load balancer and reverse proxy
Cache Server,*************,22,redis,redis789,,Redis cache server
File Server,*************,22,files,files123,,File storage and sharing
API Server,*************,22,api,api456,,REST API backend server
Queue Server,*************,22,queue,queue789,,Message queue processing
Analytics Server,192.168.1.112,22,analytics,analytics123,,Data analytics and reporting
Security Server,192.168.1.113,22,security,security456,,Security monitoring and firewall
