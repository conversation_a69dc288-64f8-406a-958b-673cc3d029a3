# 项目结构文档

## 目录树

```
// Directory tree (3 levels, limited to 200 entries)
├── .gitignore
├── .vscode/
│   └── extensions.json
├── README.md
├── index.html
├── package-lock.json
├── package.json
├── public/
│   ├── tauri.svg
│   └── vite.svg
├── src-tauri/
│   ├── .gitignore
│   ├── Cargo.toml
│   ├── build.rs
│   ├── capabilities/
│   │   └── default.json
│   ├── icons/
│   │   ├── 128x128.png
│   │   ├── <EMAIL>
│   │   ├── 32x32.png
│   │   ├── Square107x107Logo.png
│   │   ├── Square142x142Logo.png
│   │   ├── Square150x150Logo.png
│   │   ├── Square284x284Logo.png
│   │   ├── Square30x30Logo.png
│   │   ├── Square310x310Logo.png
│   │   ├── Square44x44Logo.png
│   │   ├── Square71x71Logo.png
│   │   ├── Square89x89Logo.png
│   │   ├── StoreLogo.png
│   │   ├── icon.icns
│   │   ├── icon.ico
│   │   └── icon.png
│   ├── src/
│   │   ├── commands.rs
│   │   ├── database.rs
│   │   ├── lib.rs
│   │   ├── main.rs
│   │   ├── models.rs
│   │   └── ssh_executor.rs
│   ├── target/
│   │   ├── .rustc_info.json
│   │   └── rust-analyzer/
│   └── tauri.conf.json
├── src/
│   ├── App.css
│   ├── App.tsx
│   ├── assets/
│   │   └── react.svg
│   ├── components/
│   │   └── TaskLogViewer.tsx
│   ├── main.tsx
│   ├── pages/
│   │   ├── HostManagement.tsx
│   │   └── TaskManagement.tsx
│   ├── services/
│   │   └── api.ts
│   ├── types/
│   │   └── index.ts
│   └── vite-env.d.ts
├── tsconfig.json
├── tsconfig.node.json
└── vite.config.ts
```

## 主要文件夹说明

- **src-tauri/**: Tauri后端项目目录，包含Rust源代码、配置文件和构建脚本。
  - **src/**: Rust源代码目录，包括命令处理、数据库操作、SSH执行器等。
  - **capabilities/**: Tauri能力配置文件。
  - **icons/**: 应用图标文件。

- **src/**: 前端源代码目录，使用React和TypeScript。
  - **pages/**: 页面组件，包括主机管理和任务管理。
  - **components/**: 通用组件，如任务日志查看器。
  - **services/**: API服务调用。
  - **types/**: TypeScript类型定义。

- **public/**: 静态资源文件。