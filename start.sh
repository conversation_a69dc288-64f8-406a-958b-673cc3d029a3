#!/bin/bash

# SSH Manager 启动脚本
# 用于清理端口占用、文件锁问题并启动程序

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查是否在正确的目录
check_directory() {
    if [ ! -f "package.json" ] || [ ! -d "src-tauri" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    log_info "当前目录: $(pwd)"
}

# 清理端口占用
cleanup_ports() {
    log_info "清理端口占用..."
    
    # 常用的开发端口
    PORTS=(1420 3000 5173 8080 8000 9000)
    
    for port in "${PORTS[@]}"; do
        # 查找占用端口的进程
        PID=$(lsof -ti:$port 2>/dev/null || true)
        if [ ! -z "$PID" ]; then
            log_warn "端口 $port 被进程 $PID 占用，正在终止..."
            kill -9 $PID 2>/dev/null || true
            sleep 1
            # 再次检查
            PID=$(lsof -ti:$port 2>/dev/null || true)
            if [ -z "$PID" ]; then
                log_info "端口 $port 已释放"
            else
                log_error "无法释放端口 $port"
            fi
        else
            log_debug "端口 $port 未被占用"
        fi
    done
}

# 清理 Cargo 文件锁
cleanup_cargo_locks() {
    log_info "清理 Cargo 文件锁..."
    
    # 清理 target 目录中的锁文件
    if [ -d "src-tauri/target" ]; then
        find src-tauri/target -name "*.lock" -type f -delete 2>/dev/null || true
        find src-tauri/target -name ".cargo-lock" -type f -delete 2>/dev/null || true
        log_info "已清理 target 目录中的锁文件"
    fi
    
    # 清理 Cargo 注册表锁
    CARGO_HOME=${CARGO_HOME:-$HOME/.cargo}
    if [ -d "$CARGO_HOME" ]; then
        find "$CARGO_HOME" -name "*.lock" -type f -delete 2>/dev/null || true
        log_info "已清理 Cargo 注册表锁文件"
    fi
    
    # 清理项目级别的锁文件
    if [ -f "src-tauri/Cargo.lock" ]; then
        log_debug "保留 Cargo.lock 文件（依赖锁定文件）"
    fi
}

# 清理 Node.js 相关锁文件
cleanup_node_locks() {
    log_info "清理 Node.js 锁文件..."
    
    # 清理 npm/yarn 锁文件冲突
    if [ -f "package-lock.json" ] && [ -f "yarn.lock" ]; then
        log_warn "检测到 package-lock.json 和 yarn.lock 同时存在"
        read -p "删除哪个锁文件？(1: package-lock.json, 2: yarn.lock, 3: 保留两个): " choice
        case $choice in
            1)
                rm package-lock.json
                log_info "已删除 package-lock.json"
                ;;
            2)
                rm yarn.lock
                log_info "已删除 yarn.lock"
                ;;
            *)
                log_info "保留两个锁文件"
                ;;
        esac
    fi
}

# 清理临时文件和缓存
cleanup_temp_files() {
    log_info "清理临时文件和缓存..."
    
    # 清理 node_modules/.cache
    if [ -d "node_modules/.cache" ]; then
        rm -rf node_modules/.cache
        log_info "已清理 node_modules/.cache"
    fi
    
    # 清理 Vite 缓存
    if [ -d "node_modules/.vite" ]; then
        rm -rf node_modules/.vite
        log_info "已清理 Vite 缓存"
    fi
    
    # 清理 Tauri 缓存
    if [ -d "src-tauri/target/debug/.fingerprint" ]; then
        rm -rf src-tauri/target/debug/.fingerprint
        log_info "已清理 Tauri 构建缓存"
    fi
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    log_debug "Node.js 版本: $(node --version)"
    
    # 检查包管理器
    if command -v yarn &> /dev/null; then
        PACKAGE_MANAGER="yarn"
        log_debug "使用 Yarn: $(yarn --version)"
    elif command -v npm &> /dev/null; then
        PACKAGE_MANAGER="npm"
        log_debug "使用 NPM: $(npm --version)"
    else
        log_error "未找到 npm 或 yarn"
        exit 1
    fi
    
    # 检查 Rust
    if ! command -v cargo &> /dev/null; then
        log_error "Rust/Cargo 未安装"
        exit 1
    fi
    log_debug "Cargo 版本: $(cargo --version)"
    
    # 检查 Tauri CLI
    if ! command -v cargo tauri &> /dev/null; then
        log_warn "Tauri CLI 未安装，正在安装..."
        cargo install tauri-cli
    fi
}

# 安装依赖
install_dependencies() {
    log_info "安装/更新依赖..."
    
    # 安装前端依赖
    if [ "$PACKAGE_MANAGER" = "yarn" ]; then
        yarn install
    else
        npm install
    fi
    
    # 检查 Rust 依赖
    cd src-tauri
    cargo check
    cd ..
}

# 启动开发服务器
start_dev_server() {
    log_info "启动开发服务器..."
    
    # 设置环境变量
    export RUST_LOG=debug
    export RUST_BACKTRACE=1
    
    # 启动 Tauri 开发服务器
    if [ "$PACKAGE_MANAGER" = "yarn" ]; then
        yarn tauri dev
    else
        npm run tauri dev
    fi
}

# 主函数
main() {
    log_info "SSH Manager 启动脚本开始执行..."
    
    # 检查目录
    check_directory
    
    # 清理工作
    cleanup_ports
    cleanup_cargo_locks
    cleanup_node_locks
    cleanup_temp_files
    
    # 检查和安装依赖
    check_dependencies
    install_dependencies
    
    # 启动服务器
    start_dev_server
}

# 捕获中断信号
trap 'log_warn "收到中断信号，正在清理..."; cleanup_ports; exit 0' INT TERM

# 解析命令行参数
case "${1:-}" in
    --clean-only)
        log_info "仅执行清理操作..."
        check_directory
        cleanup_ports
        cleanup_cargo_locks
        cleanup_node_locks
        cleanup_temp_files
        log_info "清理完成"
        exit 0
        ;;
    --check-only)
        log_info "仅检查依赖..."
        check_directory
        check_dependencies
        log_info "检查完成"
        exit 0
        ;;
    --help|-h)
        echo "SSH Manager 启动脚本"
        echo ""
        echo "用法: $0 [选项]"
        echo ""
        echo "选项:"
        echo "  --clean-only    仅执行清理操作"
        echo "  --check-only    仅检查依赖"
        echo "  --help, -h      显示此帮助信息"
        echo ""
        echo "默认行为: 执行完整的清理和启动流程"
        exit 0
        ;;
    "")
        # 默认行为：执行完整流程
        main
        ;;
    *)
        log_error "未知选项: $1"
        echo "使用 $0 --help 查看帮助信息"
        exit 1
        ;;
esac
