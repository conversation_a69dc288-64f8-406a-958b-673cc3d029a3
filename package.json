{"name": "s<PERSON><PERSON><PERSON>", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@tauri-apps/api": "^2.6.0", "antd": "^5.26.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.6.3", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "recharts": "^2.15.4", "ssh2": "^1.15.0", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-web-links": "^0.9.0"}, "devDependencies": {"@tauri-apps/cli": "^2.0.0", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-window": "^1.8.8", "@types/ssh2": "^1.11.15", "@vitejs/plugin-react": "^4.0.3", "terser": "^5.43.1", "typescript": "^5.0.2", "vite": "^4.4.4", "vite-plugin-eslint": "^1.8.1"}}