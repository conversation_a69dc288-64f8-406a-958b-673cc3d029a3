cargo:rerun-if-changed=build.rs
cargo:rustc-check-cfg=cfg(no_core_cstr)
cargo:rustc-check-cfg=cfg(no_core_error)
cargo:rustc-check-cfg=cfg(no_core_net)
cargo:rustc-check-cfg=cfg(no_core_num_saturating)
cargo:rustc-check-cfg=cfg(no_core_try_from)
cargo:rustc-check-cfg=cfg(no_diagnostic_namespace)
cargo:rustc-check-cfg=cfg(no_float_copysign)
cargo:rustc-check-cfg=cfg(no_num_nonzero_signed)
cargo:rustc-check-cfg=cfg(no_relaxed_trait_bounds)
cargo:rustc-check-cfg=cfg(no_serde_derive)
cargo:rustc-check-cfg=cfg(no_std_atomic)
cargo:rustc-check-cfg=cfg(no_std_atomic64)
cargo:rustc-check-cfg=cfg(no_systemtime_checked_add)
cargo:rustc-check-cfg=cfg(no_target_has_atomic)
