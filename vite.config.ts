import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

const host = process.env.TAURI_DEV_HOST as string | undefined;

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react({
      // 优化JSX运行时
      jsxRuntime: "automatic",
      // 开发时启用babel插件
      babel: {
        plugins: process.env.NODE_ENV === "development"
        ? []
        : [["import", { libraryName: "antd", style: false }]] as any,
      },
    }),
  ],

  // 路径别名配置
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      "@components": path.resolve(__dirname, "./src/components"),
      "@pages": path.resolve(__dirname, "./src/pages"),
      "@services": path.resolve(__dirname, "./src/services"),
      "@hooks": path.resolve(__dirname, "./src/hooks"),
      "@types": path.resolve(__dirname, "./src/types"),
      "@contexts": path.resolve(__dirname, "./src/contexts"),
    },
  },



  // 优化依赖预构建
  optimizeDeps: {
    include: [
      "react",
      "react-dom",
      "antd",
      "react-router-dom",
      "@ant-design/icons",
      "xterm",
      "xterm-addon-fit",
      "xterm-addon-web-links",
    ],
    // 排除不需要预构建的依赖
    exclude: ["@tauri-apps/api"],
  },

  // 构建优化
  build: {
    // 设置chunk大小警告限制
    chunkSizeWarningLimit: 1000,
    // 启用CSS代码分割
    cssCodeSplit: true,
    // 生成source map
    sourcemap: process.env.NODE_ENV === "development",
    // 构建输出优化
    rollupOptions: {
      output: {
        // 手动分割代码块
        manualChunks: {
          "react-vendor": ["react", "react-dom"],
          "antd-vendor": ["antd", "@ant-design/icons"],
          "router-vendor": ["react-router-dom"],
          "terminal-vendor": [
            "xterm",
            "xterm-addon-fit",
            "xterm-addon-web-links",
          ],
        },
        // 优化chunk文件名
        chunkFileNames: "assets/js/[name]-[hash].js",
        entryFileNames: "assets/js/[name]-[hash].js",
        assetFileNames: "assets/[ext]/[name]-[hash].[ext]",
      },
    },
    // 压缩配置
    minify: "terser",
    terserOptions: {
      compress: {
        drop_console: process.env.NODE_ENV === "production",
        drop_debugger: true,
      },
    },
  },

  // Vite options tailored for Tauri development and only applied in `tauri dev` or `tauri build`
  //
  // 1. prevent vite from obscuring rust errors
  clearScreen: false,
  // 2. tauri expects a fixed port, fail if that port is not available
  server: {
    port: 1420,
    strictPort: true,
    host: host || false,
    // 启用文件系统缓存
    fs: {
      strict: true,
    },
    hmr: host
      ? {
          protocol: "ws",
          host,
          port: 1421,
        }
      : undefined,
    watch: {
      // 排除不需要监听的目录和文件
      ignored: [
        '**/src-tauri/**',
        '**/src-tauri/log/**',
        '**/node_modules/**',
        '**/.git/**',
        '**/dist/**',
        '**/db.db*'
      ],
      // 优化文件监听
      usePolling: false,
    },
  },

  // CSS优化
  css: {
    // 启用CSS模块
    modules: {
      localsConvention: "camelCase" as const,
    },
    // PostCSS配置
    postcss: {
      plugins: [
        // 可以添加autoprefixer等插件
      ],
    },
    // 预处理器配置
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
        modifyVars: {
          // Ant Design主题定制
          "@primary-color": "#1890ff",
        },
      },
    },
  },

  // ESBuild配置
  esbuild: {
    // 移除console和debugger (生产环境)
    drop: process.env.NODE_ENV === "production" ? ["console", "debugger"] : [],
    // 设置目标环境
    target: "es2020",
  },

  // 定义全局常量
  define: {
    __DEV__: process.env.NODE_ENV === "development",
    __PROD__: process.env.NODE_ENV === "production",
  },
});
