#!/bin/bash

# SSH Manager 快速启动脚本
# 简化版本，快速清理并启动

echo "🚀 SSH Manager 快速启动..."

# 清理端口
echo "🧹 清理端口占用..."
for port in 1420 3000 5173; do
    PID=$(lsof -ti:$port 2>/dev/null || true)
    if [ ! -z "$PID" ]; then
        echo "  终止端口 $port 上的进程 $PID"
        kill -9 $PID 2>/dev/null || true
    fi
done

# 清理 Cargo 锁
echo "🔓 清理 Cargo 文件锁..."
find src-tauri/target -name "*.lock" -type f -delete 2>/dev/null || true
find ~/.cargo -name "*.lock" -type f -delete 2>/dev/null || true

# 清理缓存
echo "🗑️  清理缓存..."
rm -rf node_modules/.cache 2>/dev/null || true
rm -rf node_modules/.vite 2>/dev/null || true

# 启动
echo "▶️  启动开发服务器..."
export RUST_LOG=debug
export RUST_BACKTRACE=1

if command -v yarn &> /dev/null; then
    yarn tauri dev
else
    npm run tauri dev
fi
